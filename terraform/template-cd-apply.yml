parameters:
- name: outPutPathPlan
  type: 'string'
  displayName: '<PERSON>retório que o plan to terraform será aplicado'
  default: 'outputplan'
- name: azRmResourceGroup
  type: 'string'
  displayName: 'Resource Group do ARM'
  default: ''
- name: azRmStorageAccount
  type: 'string'
  displayName: 'Storage Account do ARM'
  default: ''
- name: azRmContainer
  type: 'string'
  displayName: 'Nome do Container no Storage Account'
  default: ''
- name: azRmKey
  type: 'string'
  displayName: 'Key de Acesso do Storage Account'
  default: ''
- name: backendServiceArm
  type: 'string'
  displayName: 'Subscription Backend - Service Connection'
  default: ''
- name: "environmentServiceNameAzureRM"
  displayName: 'Name of the Project'
  default: ''
  type: string 
- name: variables
  type: object
  default: []
  displayName: "Variables Stage"
- name: environment
  type: string  
  displayName: "Environment"
- name: packagename
  displayName: 'Name of the Project'
  default: ''
  type: string 
- name: workingDirectory
  type: 'string'
  displayName: 'Diretório base dos arquivos terraform'
  default: './'
- name: fileVariables
  type: 'string'
  displayName: 'Diretório base dos arquivos terraform'
  default: 'InputVariables.tfvars'

jobs:
- deployment: cd_apply_${{parameters.environment}}
  environment: ${{ parameters.environment }}  
  variables: 
  - template: ../configuration/config.yaml
    parameters: 
      deployCluster: terraform      
      environment: ${{ parameters.environment }}       
  strategy:
    runOnce:
      deploy:
        steps:
        - task: charleszipp.azure-pipelines-tasks-terraform.azure-pipelines-tasks-terraform-installer.TerraformInstaller@0
          inputs:
            terraformVersion: $(terraformeVersion)    
        - task: CopyFiles@2
          inputs:
            SourceFolder: '$(Agent.BuildDirectory)/TFFiles/'
            Contents: '**'
            TargetFolder: .
        - task: CopyFiles@2
          inputs:
            SourceFolder: '$(Agent.BuildDirectory)/TFPlan/'
            Contents: '**'
            TargetFolder: ${{parameters.workingDirectory}}    
        - task: qetza.replacetokens.replacetokens-task.replacetokens@3
          displayName: 'Replace Tokens in ${{parameters.fileVariables}}'
          inputs:
            targetFiles: '${{parameters.workingDirectory}}/${{parameters.fileVariables}}'
            writeBOM: false
        - task: TerraformTaskV1@0
          displayName: 'Terraform Init'
          inputs:
            provider: 'azurerm'
            command: 'init'  
            workingDirectory: ${{parameters.workingDirectory}}
            backendAzureRmResourceGroupName: ${{parameters.azRmResourceGroup}}
            backendAzureRmStorageAccountName: ${{parameters.azRmStorageAccount}}
            backendAzureRmContainerName: ${{parameters.azRmContainer}}
            backendAzureRmKey: ${{parameters.azRmKey}}
            backendServiceArm: ${{parameters.backendServiceArm}}

        - task: TerraformTaskV1@0
          displayName: 'Terraform Apply'
          inputs:
            provider: 'azurerm'
            command: 'apply'
            commandOptions: '${{parameters.outPutPathPlan}}'  
            workingDirectory: ${{parameters.workingDirectory}}
            backendAzureRmResourceGroupName: ${{parameters.azRmResourceGroup}}
            backendAzureRmStorageAccountName: ${{parameters.azRmStorageAccount}}
            backendAzureRmContainerName: ${{parameters.azRmContainer}}
            backendAzureRmKey: ${{parameters.azRmKey}}
            backendServiceArm: ${{parameters.backendServiceArm}}      
            environmentServiceNameAzureRM: ${{parameters.environmentServiceNameAzureRM}}
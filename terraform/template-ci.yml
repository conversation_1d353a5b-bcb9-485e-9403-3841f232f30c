parameters:
- name: outPutPathPlan
  type: 'string'
  displayName: '<PERSON>retó<PERSON> que o plan to terraform será aplicado'
  default: 'outputplan'
- name: workingDirectory
  type: 'string'
  displayName: 'Diretório base dos arquivos terraform'
  default: './'
- name: azRmResourceGroup
  type: 'string'
  displayName: 'Resource Group do ARM'
  default: ''
- name: azRmStorageAccount
  type: 'string'
  displayName: 'Storage Account do ARM'
  default: ''
- name: azRmContainer
  type: 'string'
  displayName: 'Nome do Container no Storage Account'
  default: ''
- name: azRmKey
  type: 'string'
  displayName: 'Key de Acesso do Storage Account'
  default: ''
- name: backendServiceArm
  type: 'string'
  displayName: 'Subscription Backend - Service Connection'
  default: ''
- name: planArguments
  type: 'string'
  displayName: 'Arguments for Plan command'
  default: ''
- name: fileVariables
  type: 'string'
  displayName: 'Diretório base dos arquivos terraform'
  default: 'InputVariables.tfvars'

jobs:
- job: "build"  
  displayName: "Build a Project Package"
  variables: 
  - template: ../configuration/config.yaml
    parameters: 
      deployCluster: terraform      
  steps:
  - task: charleszipp.azure-pipelines-tasks-terraform.azure-pipelines-tasks-terraform-installer.TerraformInstaller@0
    inputs:
      terraformVersion: $(terraformeVersion)
  - task: PublishBuildArtifacts@1
    displayName: Publish TFFile
    inputs:
      PathtoPublish: $(System.DefaultWorkingDirectory)/
      ArtifactName: TFFiles
  - task: qetza.replacetokens.replacetokens-task.replacetokens@3
    displayName: 'Replace Tokens in ${{parameters.fileVariables}}'
    inputs:
      targetFiles: '${{parameters.workingDirectory}}/${{parameters.fileVariables}}'
      writeBOM: false        
  - task: TerraformTaskV1@0
    displayName: 'Terraform Init'
    inputs:
      provider: 'azurerm'
      command: 'init'      
      workingDirectory: ${{parameters.workingDirectory}}
      backendAzureRmResourceGroupName: ${{parameters.azRmResourceGroup}}
      backendAzureRmStorageAccountName: ${{parameters.azRmStorageAccount}}
      backendAzureRmContainerName: ${{parameters.azRmContainer}}
      backendAzureRmKey: ${{parameters.azRmKey}}
      backendServiceArm: ${{parameters.backendServiceArm}}      

  - task: TerraformTaskV1@0
    displayName: 'Terraform Plan'
    inputs:
      provider: 'azurerm'
      command: 'plan'
      commandOptions: ' -out=${{parameters.outPutPathPlan}} ${{parameters.planArguments}}'
      workingDirectory: ${{parameters.workingDirectory}}
      backendAzureRmResourceGroupName: ${{parameters.azRmResourceGroup}}
      backendAzureRmStorageAccountName: ${{parameters.azRmStorageAccount}}
      backendAzureRmContainerName: ${{parameters.azRmContainer}}
      backendAzureRmKey: ${{parameters.azRmKey}}
      backendServiceArm: ${{parameters.backendServiceArm}}      
      environmentServiceNameAzureRM: ${{parameters.backendServiceArm}}

  - task: PublishBuildArtifacts@1
    displayName: Publish TFPlan
    inputs:
      PathtoPublish: ${{parameters.workingDirectory}}/${{parameters.outPutPathPlan}}
      ArtifactName: TFPlan
parameters:
- name: outPutPathPlan
  type: 'string'
  displayName: '<PERSON><PERSON><PERSON><PERSON> que o plan to terraform será aplicado'
  default: 'outputplan'
- name: azRmResourceGroup
  type: 'string'
  displayName: 'Resource Group do ARM'
  default: ''
- name: azRmStorageAccount
  type: 'string'
  displayName: 'Storage Account do ARM'
  default: ''
- name: azRmContainer
  type: 'string'
  displayName: 'Nome do Container no Storage Account'
  default: ''
- name: azRmKey
  type: 'string'
  displayName: 'Key de Acesso do Storage Account'
  default: ''
- name: backendServiceArm
  type: 'string'
  displayName: 'Subscription Backend - Service Connection'
  default: ''
- name: "environmentServiceNameAzureRM"
  displayName: 'Name of the Project'
  default: ''
  type: string 
- name: environment
  type: string  
  displayName: "Environment"
- name: packagename
  displayName: 'Name of the Project'
  default: ''
  type: string

jobs:
- deployment: cd_destroy_${{parameters.environment}}  
  environment: ${{ parameters.environment }}
  variables: 
  - template: ../configuration/config.yaml
    parameters: 
      deployCluster: terraform      
      environment: ${{ parameters.environment }}  
  strategy:
    runOnce:
      deploy:
        steps:
        - task: charleszipp.azure-pipelines-tasks-terraform.azure-pipelines-tasks-terraform-installer.TerraformInstaller@0
          inputs:
            terraformVersion: $(terraformeVersion)
        - task: CopyFiles@2
          inputs:
            SourceFolder: '$(Agent.BuildDirectory)/TFFiles/'
            Contents: '**'
            TargetFolder: '$(System.DefaultWorkingDirectory)'    
        - task: TerraformTaskV1@0
          displayName: 'Terraform Init'
          inputs:
            provider: 'azurerm'
            command: 'init'      
            workingDirectory: '$(System.DefaultWorkingDirectory)'      
            backendAzureRmResourceGroupName: ${{parameters.azRmResourceGroup}}
            backendAzureRmStorageAccountName: ${{parameters.azRmStorageAccount}}
            backendAzureRmContainerName: ${{parameters.azRmContainer}}
            backendAzureRmKey: ${{parameters.azRmKey}}
            backendServiceArm: ${{parameters.backendServiceArm}}     

        - task: TerraformTaskV1@0
          displayName: 'Terraform Destroy'
          inputs:
            provider: 'azurerm'
            command: 'destroy'            
            workingDirectory: '$(System.DefaultWorkingDirectory)'
            backendAzureRmResourceGroupName: ${{parameters.azRmResourceGroup}}
            backendAzureRmStorageAccountName: ${{parameters.azRmStorageAccount}}
            backendAzureRmContainerName: ${{parameters.azRmContainer}}
            backendAzureRmKey: ${{parameters.azRmKey}}
            backendServiceArm: ${{parameters.backendServiceArm}}      
            environmentServiceNameAzureRM: ${{parameters.environmentServiceNameAzureRM}}
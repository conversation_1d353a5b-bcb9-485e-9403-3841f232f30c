parameters:
  - name: pythonVersion
    type: string
    default: '3.x' 
  - name: pythonCmds
    type: object
    default: ''
  - name: skipSonarBranch
    type: boolean
  
steps:
  - ${{ if eq(parameters.skipSonarBranch, false) }}:
    - template: ../si/pre-steps-template.yml
      parameters:
        type: python
  - task: UsePythonVersion@0
    inputs:
      versionSpec: '${{ parameters.pythonVersion }}'
  - ${{ each cmd in parameters.pythonCmds }}:
    - task: PowerShell@2
      condition: succeeded()
      displayName: '${{ cmd }}'
      inputs:
        targetType: "inline"
        script: |
          Invoke-Expression "${{ cmd }}"
  - ${{ if eq(parameters.skipSonarBranch, false) }}:
    - template: ../si/pos-steps-template.yml
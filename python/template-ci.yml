parameters:
  - name: pythonVersion
    type: string
    default: '3.x' 
  - name: pypicom
    type: string
    default: ''
  - name: pythonCmds
    type: object
    default: ''
  - name: skipSonarBranch
    type: boolean    

jobs:
  - job: Publishing
    steps:
    - task: PowerShell@2
      displayName: 'Validando Variável {TWINE_USERNAME} '
      inputs:
        targetType: 'inline'
        script: 'if ([string]::IsNullOrWhitespace($env:TWINE_USERNAME)) { throw "Não foi fornecida a variável {TWINE_USERNAME} para integração pypi" }'
    - task: PowerShell@2
      displayName: 'Validando Variável {TWINE_PASSWORD} '
      inputs:
        targetType: 'inline'
        script: 'if ([string]::IsNullOrWhitespace($env:TWINE_PASSWORD)) { throw "Não foi fornecida a variável {TWINE_PASSWORD} para integração pypi" }'
    - template: python-commands.yml
      parameters:        
        pythonVersion: ${{ parameters.pythonVersion }}
        pythonCmds: ${{ parameters.pythonCmds }}
        skipSonarBranch: ${{ parameters.skipSonarBranch }}


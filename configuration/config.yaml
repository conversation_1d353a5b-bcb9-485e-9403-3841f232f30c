parameters:
- name: deployCluster
  type: string
  default: kubernetes
- name: environment
  type: string
  default: staging
- name: from
  type: string
  default: ''
  
variables:
- ${{ if ne(parameters.from, '') }}:
  - template: ${{parameters.from}}-config.yaml
    parameters:
      environment: ${{parameters.environment}}  
      deployCluster: ${{parameters.deployCluster}}
- template: ${{parameters.deployCluster}}-config.yaml
  parameters:    
    environment: ${{parameters.environment}}    
  
           


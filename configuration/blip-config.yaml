parameters:
- name: deployCluster
  type: string
  default: kubernetes
- name: environment
  type: string
  default: staging

variables:
  isFromUPT: true  
  ${{ if eq(parameters.deployCluster, 'kubernetes') }}:  
    acrSecretName: "acr-takecustomersuccess"
    registryLogin: "takecustomersuccess"
    registryName: "takecustomersuccess"
    registryPassword: "1k1qvHg/WqXfjq3yRzZqCIZ5T6xRQIrX"
    NuGetPassword: "4cpmcmx4ciool675wtrkd6yzbw63mg63a54fghmf3dvwt7iutz2q"
    NuGetUsername: "<EMAIL>"

  ${{ if eq(parameters.environment, 'staging') }}:  
    hostName: "-hmg.blip.tools"
    namespace: "staging"
    secretName: "blip.tools.tls"
    Serilog.WriteTo.0.Args.serverUrl: "https://hmg-seq.blip.tools"

  ${{ if eq(parameters.environment, 'beta') }}:  
    hostName: "-beta.blip.tools"
    namespace: "beta"
    secretName: "blip.tools.tls"
    Serilog.WriteTo.0.Args.serverUrl: "https://hmg-seq.blip.tools"

  ${{ if eq(parameters.environment, 'production') }}:
    hostName: ".blip.tools"
    namespace: "prod"
    secretName: "blip.tools.tls"
    Serilog.WriteTo.0.Args.serverUrl: "https://seq.blip.tools"  
  
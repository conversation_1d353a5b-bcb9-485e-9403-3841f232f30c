parameters:
- name: environment
  type: string
  default: staging
variables:
  namehelmVersion: "v3.4.0"
  averageCpuUtilization: "60"
  ${{ if ne(parameters.environment, 'production') }}:
    limitCpu: "150m"
    limitMemory: "2Gi"
    maxReplicas: 1
    minReplicas: 1
    requestsCpu: "50m"
    requestsMemory: "128Mi"

  ${{ if eq(parameters.environment, 'production') }}:    
    limitCpu: "2000m"
    limitMemory: "2Gi"
    maxReplicas: 4
    minReplicas: 2
    requestsCpu: "100m"
    requestsMemory: "256Mi" 


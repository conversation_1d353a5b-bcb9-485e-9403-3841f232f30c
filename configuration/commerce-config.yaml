parameters:
- name: deployCluster
  type: string
  default: kubernetes
- name: environment
  type: string
  default: staging

variables:
  isFromUPT: true  
  ${{ if eq(parameters.deployCluster, 'kubernetes') }}: 
    acrSecretName: "crcommhmgeastus2001"
    registryLogin: "crcommhmgeastus2001"
    registryName: "crcommhmgeastus2001"
    registryPassword: "c85vhreMsBa72gVt+9FFBWRdMReMCHW0Ifrd0Wo0n3+ACRBVNEk8"
    NuGetPassword: "4cpmcmx4ciool675wtrkd6yzbw63mg63a54fghmf3dvwt7iutz2q"
    NuGetUsername: "<EMAIL>"

  ${{ if eq(parameters.environment, 'staging') }}:  
    hostName: ".hmg-commerce.blip.ai"
    namespace: "staging"
    # secretName: "packs.blip.ai.tls"
    Serilog.WriteTo.0.Args.serverUrl: "http://hmg-az-seq:5341"

  ${{ if eq(parameters.environment, 'beta') }}:  
    hostName: ".hmg-commerce.blip.ai"
    namespace: "staging"
    # secretName: "packs.blip.ai.tls"
    Serilog.WriteTo.0.Args.serverUrl: "http://hmg-az-seq:5341"

  ${{ if eq(parameters.environment, 'production') }}:
    hostName: ".commerce.blip.ai"
    namespace: "staging"
    # secretName: "packs.blip.ai.tls"
    Serilog.WriteTo.0.Args.serverUrl: "http://az-seq:5341"  
  
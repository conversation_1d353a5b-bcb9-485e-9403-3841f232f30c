parameters:
- name: deployCluster
  type: string
  default: kubernetes
- name: environment
  type: string
  default: staging

variables:
  isFromUPT: true  
  ${{ if eq(parameters.deployCluster, 'kubernetes') }}: 
    acrSecretName: "crdevhmgeastus"
    registryLogin: "crdevhmgeastus"
    registryName: "crdevhmgeastus"
    registryPassword: "iCDDs3BgpKsM93uJjfuueG60JeEwyLWQFWWHBRS+E1+ACRCj6UxL"
    NuGetPassword: "4cpmcmx4ciool675wtrkd6yzbw63mg63a54fghmf3dvwt7iutz2q"
    NuGetUsername: "<EMAIL>"

  ${{ if eq(parameters.environment, 'staging') }}:  
    hostName: ".hmg-packs.blip.ai"
    namespace: "staging"
    # secretName: "packs.blip.ai.tls"
    Serilog.WriteTo.0.Args.serverUrl: "http://hmg-az-seq:5341"

  ${{ if eq(parameters.environment, 'beta') }}:  
    hostName: ".beta-packs.blip.ai"
    namespace: "staging"
    # secretName: "packs.blip.ai.tls"
    Serilog.WriteTo.0.Args.serverUrl: "http://hmg-az-seq:5341"

  ${{ if eq(parameters.environment, 'production') }}:
    hostName: ".packs.blip.ai"
    namespace: "staging"
    # secretName: "packs.blip.ai.tls"
    Serilog.WriteTo.0.Args.serverUrl: "http://az-seq:5341"  
  
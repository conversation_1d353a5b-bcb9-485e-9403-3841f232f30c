parameters:
- name: deployCluster
  type: string
  default: kubernetes
- name: environment
  type: string
  default: staging

variables:
  isFromUPT: true  
  ${{ if eq(parameters.deployCluster, 'kubernetes') }}:  
    acrSecretName: "acr-takecustomersuccess"
    registryLogin: "takecustomersuccess"
    registryName: "takecustomersuccess"
    registryPassword: "1k1qvHg/WqXfjq3yRzZqCIZ5T6xRQIrX"
    NuGetPassword: "4cpmcmx4ciool675wtrkd6yzbw63mg63a54fghmf3dvwt7iutz2q"
    NuGetUsername: "<EMAIL>"

  ${{ if eq(parameters.environment, 'staging') }}:
    chartName: blip-csps-hmg
    hostName: ".hmg-cs.blip.ai"
    namespace: "staging"
    secretName: "hmg-cs.blip.ai"
    Serilog.WriteTo.0.Args.serverUrl: "http://hmg-az-seq:5341"

  ${{ if eq(parameters.environment, 'beta') }}:
    chartName: blip-csps-hmg
    hostName: ".beta-cs.blip.ai"
    namespace: "beta"
    secretName: "beta-cs.blip.ai"
    Serilog.WriteTo.0.Args.serverUrl: "http://hmg-az-seq:5341"

  ${{ if eq(parameters.environment, 'production') }}:
    chartName: blip-csps-prod
    hostName: ".cs.blip.ai"
    namespace: "prod"
    secretName: "cs.blip.ai"
    Serilog.WriteTo.0.Args.serverUrl: "http://az-seq:5341"
  
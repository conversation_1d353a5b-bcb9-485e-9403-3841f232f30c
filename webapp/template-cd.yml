parameters:
- name: environment
  type: string  
  displayName: "Environment"
- name: serviceConnection
  type: string  
  displayName: "Service Connection Name - Deploy"
- name: variables
  type: object
  default: []
  displayName: "Variables Stage"
- name: webAppName
  type: string
  default: ''
- name: webAppAppSettings
  type: string
  default: ''
- name: webAppAppType
  type: string
  default: 'webApp'
- name: webAppPackage
  type: string
  default: '$(Pipeline.Workspace)/**/*.zip'
- name: webAppTakeAppOffline
  type: boolean
  default: true
- name: webAppUseWebDeploy
  type: boolean
  default: true
- name: webAppRemoveAdditionalFiles
  type: boolean
  default: true
- name: webAppRenameFiles
  type: boolean
  default: true
- name: webAppJSONFiles
  type: string
  default: '*/settings.json'

jobs:
- deployment: cd_${{ parameters.environment }}
  pool:
    vmImage: 'windows-latest'
  environment: ${{ parameters.environment }}
  variables:
  - ${{ each variable in parameters.variables }}:
    - group: ${{ variable }}
  strategy:
    runOnce:
      deploy:
        steps:
        - task: AzureRmWebAppDeployment@3
          displayName: 'Azure App Service Deploy: ${{ parameters.webAppName }}'
          inputs:
            appType: ${{ parameters.webAppAppType }}
            azureSubscription: ${{ parameters.serviceConnection }}
            WebAppName: ${{ parameters.webAppName }}
            Package: ${{ parameters.webAppPackage }}
            AppSettings: ${{ parameters.webAppAppSettings }}
            TakeAppOfflineFlag: ${{ parameters.webAppTakeAppOffline }}
            UseWebDeploy: ${{ parameters.webAppUseWebDeploy }}
            RemoveAdditionalFilesFlag: ${{ parameters.webAppRemoveAdditionalFiles }}
            RenameFilesFlag: ${{ parameters.webAppRenameFiles }}
            JSONFiles: ${{ parameters.webAppJSONFiles }}
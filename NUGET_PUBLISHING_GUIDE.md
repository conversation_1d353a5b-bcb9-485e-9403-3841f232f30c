# NuGet Package Publishing in Azure DevOps Pipelines

## Overview

This guide explains the new NuGet package publishing functionality added to the Azure DevOps pipeline templates. The enhancement allows applications using the `octopus` target to automatically package and publish NuGet packages to Azure Artifacts feeds.

## What Was Added

### New Parameters

The following parameter has been added to `template-pipeline.yml`:

- **`publishNugetPackages`** (boolean, default: false)
  - Enables NuGet package publishing to the configured vstsFeed
  - When set to `true`, the pipeline will pack and publish NuGet packages
  - Automatically includes authentication and symbol packages

### Implementation Details

The NuGet packaging and publishing steps are added to the `Octopus_Build_Push` job in `octopus/template-ci-steps.yml`:

1. **Authentication**: Always uses `NuGetAuthenticate@1` task for secure feed access
2. **Packaging**: Uses `DotNetCoreCLI@2` with `pack` command on the solution level
3. **Symbol Packages**: Always includes symbol packages for debugging support
4. **Publishing**: Uses `DotNetCoreCLI@2` with `push` command to internal Azure Artifacts feed
5. **Versioning**: Uses `$(ProductBuildNumber)` with `-beta` suffix for beta branches
6. **Conditional Execution**: Only runs on main branches (master/main/hotfix/beta)

## How to Use

### Basic Configuration

Add the following parameters to your pipeline YAML:

```yaml
extends:
  template: "template-pipeline.yml@templates"
  parameters:
    # ... existing parameters ...
    vstsFeed: "YourFeedName"
    publishNugetPackages: true
```

### Example Pipeline

Here's an example based on the provided Iris Scheduler pipeline:

```yaml
trigger:
  branches:
   include:
     - master
  paths:
    include:
      - "src/Applications/Scheduler"

resources:
  repositories:
    - repository: templates
      type: git
      name: Operações/template-take-blip
      ref: refs/tags/v1.4.75

variables:
  BuildCounter: $[counter('buildCounter',1)]
  framework: dotnet
  NUGET_PACKAGES: $(Pipeline.Workspace)/.nuget/packages

pool:
  name: 'Azure Pipelines'
  vmImage: windows-latest

extends:
  template: "template-pipeline.yml@templates"
  parameters:
    vstsFeed: "BlipNuget"
    publishNugetPackages: true
    packagename: "Iris-Scheduler"
    type: dotnet
    to: octopus
    # ... other existing parameters ...
```

## Technical Details

### Package Discovery

The implementation uses the `solution` parameter (default: `**/*.sln`) to pack all packable projects in the solution. This means:

- All projects with `<IsPackable>true</IsPackable>` (or implicitly packable) will be packed
- Test projects and non-packable projects are automatically excluded
- No need to specify individual .csproj files

### Versioning Strategy

- **Regular branches**: Uses `$(ProductBuildNumber)` as the package version
- **Beta branches**: Uses `$(ProductBuildNumber)-beta` as the package version
- Version is set via `PackageVersion` MSBuild property

### Output Location

- NuGet packages are created in `$(Build.ArtifactStagingDirectory)/nuget/`
- Symbol packages are always included in packaging but excluded from publishing

### Branch Conditions

NuGet publishing only occurs on:
- `master` branch
- `main` branch
- Branches containing `hotfix`
- Branches containing `beta`

## Benefits

1. **Unified Pipeline**: Single pipeline handles both Octopus deployment and NuGet publishing
2. **Consistent Versioning**: Uses the same versioning strategy as Octopus packages
3. **Automatic Discovery**: Packs all packable projects in the solution automatically
4. **Latest Tasks**: Uses the latest Azure DevOps tasks (`DotNetCoreCLI@2`, `NuGetAuthenticate@1`)
5. **Conditional Publishing**: Only publishes on appropriate branches
6. **Feed Integration**: Seamlessly integrates with existing Azure Artifacts feeds

## Migration from Existing NuGet Pipelines

If you have existing dedicated NuGet pipelines, you can now consolidate them by:

1. Adding the new parameters to your main application pipeline
2. Removing the separate NuGet pipeline (if desired)
3. Ensuring your projects have proper `<IsPackable>` settings

This approach reduces pipeline maintenance overhead while providing the same functionality.

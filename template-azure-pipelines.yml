# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

parameters:
  - name: packagename
    type: string
    displayName: "Nome do Projeto"
  - name: dockerfiledir
    type: string
    default: "**/Dockerfile"
    displayName: "Dockerfile Dir, default is **/Dockerfile"
  - name: contextpath
    type: string
    default: "."
    displayName: "Context of Docker, default is ."
  - name: octopuspackagename
    type: string
    displayName: "Deploy Octopus project name"
  - name: BuildCounter
    displayName: "Counter of build"
  - name: octopusSpace
    type: string
    default: "Spaces-1"
  - name: configPath
    type: string

jobs:
  - job: Docker_Build_Send_To_Hub
    steps:
      - task: PowerShell@2
        inputs:
          targetType: "inline"
          script: |
            # Write your PowerShell commands here.

            Write-Host  ${{ parameters.BuildCounter }}
      - task: PowerShell@2
        inputs:
          targetType: "inline"
          script: |
            function Get-ISOWeekNumber([DateTime] $Date)
            {
                if ($Date.DayOfWeek -ge [DayOfWeek]::Sunday -And $Date.DayOfWeek -le [DayOfWeek]::Wednesday)
                {
                    $Date = $Date.AddDays(3)
                }

                return [System.Globalization.CultureInfo]::InvariantCulture.Calendar.GetWeekOfYear($Date, [System.Globalization.CalendarWeekRule]::FirstFourDayWeek, [DayOfWeek]::Monday)
            }

            function Get-MonthWeekNumber([DateTime] $Date)
            {
                $WeekOfYear = Get-ISOWeekNumber($Date)

                if ($Date.Month -eq 1)
                {
                    return $WeekOfYear
                }

                $FirstDayOfMonth = $Date.AddDays(1 - $Date.Day)
                $FirstDayOfMonthWeekOfYear = Get-ISOWeekNumber($FirstDayOfMonth)
                return $WeekOfYear - $FirstDayOfMonthWeekOfYear + 1

            }

            $Today = Get-Date -Hour 0 -Minute 0 -Second 0
            Write-Host "Today is $Today"

            $ShortYear = $Today.ToString("yy")
            Write-Host "Today short year is $ShortYear"

            $Month = $Today.Month
            Write-Host "Today month is $Month"

            $WeekOfMonth = Get-MonthWeekNumber($Today)
            Write-Host "Today week of month is $WeekOfMonth"

            $ReleaseNumber = "$ShortYear.$Month$WeekOfMonth"
            Write-Host "BLiP release number is $ReleaseNumber"

            $BuildCounter = "$(BuildCounter)"
            $BuildNumber = "$ReleaseNumber.$BuildCounter"

            Write-Host "BLiP build number is $BuildNumber"
            Write-Host "##vso[task.setvariable variable=ProductBuildNumber]$BuildNumber"
      - task: Docker@2
        inputs:
          containerRegistry: "Blip ACR - blipcontainerregistry"
          repository: "${{ parameters.packagename }}"
          command: "buildAndPush"
          Dockerfile: ${{ parameters.dockerfiledir }}
          buildContext: ${{ parameters.contextpath }}
          tags: $(ProductBuildNumber)
      - task: PowerShell@2
        condition: and(succeeded(), ne('${{ parameters.configPath }}', ''))
        inputs:
          targetType: "inline"
          script: |
            echo $(ProductBuildNumber)
            mkdir $(Build.SourcesDirectory)/Y29uZmlnU3JjCg==
            cp $(Build.SourcesDirectory)/${{ parameters.configPath }} $(Build.SourcesDirectory)/Y29uZmlnU3JjCg==/
      - task: OctopusPack@4
        condition: and(succeeded(), ne('${{ parameters.configPath }}', ''))
        inputs:
          PackageId: ${{ parameters.packagename }}__config
          PackageFormat: "Zip"
          PackageVersion: "$(ProductBuildNumber)"
          SourcePath: "$(Build.SourcesDirectory)/Y29uZmlnU3JjCg=="
          OutputPath: "$(Build.ArtifactStagingDirectory)"
      - task: OctopusPush@4
        condition: and(succeeded(), ne('${{ parameters.configPath }}', ''))
        inputs:
          OctoConnectedServiceName: "blip.octopus.app"
          Space: ${{ parameters.octopusSpace }}
          Package: "$(Build.ArtifactStagingDirectory)/${{ parameters.packagename }}__config.$(ProductBuildNumber).zip"
          Replace: "false"
      - task: OctopusCreateRelease@4
        inputs:
          OctoConnectedServiceName: "blip.octopus.app"
          Space: ${{ parameters.octopusSpace }}
          ProjectName: ${{ parameters.octopuspackagename }}
          ReleaseNumber: $(ProductBuildNumber)
          Channel:
          AdditionalArguments:

parameters:
- name: nameTag
  type: 'string'
  displayName: 'Nome da tag'
- name: isHotfix
  type: 'boolean'
  displayName: 'É um hotfix'
- name: isBeta
  type: 'boolean'
  displayName: 'É uma release beta'
- name: usePrefix
  type: 'boolean'
  displayName: 'Utilizar Prefixo de tag'
steps:
  - bash: |        
      if [[ '${{ parameters.usePrefix }}' == 'True' ]];
      then
        if [[ '${{ parameters.isHotfix }}' == 'True' ]];
        then
          echo "##vso[task.setvariable variable=tagPrefix]hotfix-"
        elif [[ '${{ parameters.isBeta }}' == 'True' ]];
        then
          echo "##vso[task.setvariable variable=tagPrefix]beta-"
        else
          echo "##vso[task.setvariable variable=tagPrefix]v"
        fi
      else
        echo "##vso[task.setvariable variable=tagPrefix]"
      fi      
    displayName: 'Set Prefix Tag'
  - script: |
      git tag $(tagPrefix)${{parameters.nameTag}} 
      git push origin $(tagPrefix)${{parameters.nameTag}} 
    workingDirectory: $(Build.SourcesDirectory)
    displayName: 'Create and Push Tag Value'
  - task: tagBuildOrRelease@0
    displayName: 'Create Build Tag'
    inputs:
      type: 'Build'
      tags: |
        $(tagPrefix)${{parameters.nameTag}}
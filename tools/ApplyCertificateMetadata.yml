parameters:
  - name: serviceConnection
    type: string  
    displayName: "Service Connection Name - Deploy"
steps:
  # Create certificate-metadata.yaml
  - task: PowerShell@2
    displayName: 'Create certificate-metadata.yaml'
    inputs:
      targetType: 'inline'
      script: |
        $json_template = @"
        apiVersion: cert-manager.io/v1
        kind: Certificate
        metadata:
          name: "#{certificateMetadataName}#"
        spec:
          secretName: "#{secretName}#"
          duration: 2160h
          renewBefore: 360h
          commonName: "#{projectName}##{hostName}#"
          usages:
            - server auth
            - client auth
          dnsNames:
            - "#{projectName}##{hostName}#"
          issuerRef:
            name: letsencrypt-production
            kind: ClusterIssuer
          secretTemplate:
            annotations:
              kubed.appscode.com/sync: ""
        "@
        echo $json_template > ./certificate-metadata.yaml
        echo applying certificate-metadata.yaml
        cat ./certificate-metadata.yaml

  # Replace tokens in certificate metadata
  - task: qetza.replacetokens.replacetokens-task.replacetokens@5
    condition: succeeded()
    displayName: 'Replace tokens in certificate file'
    inputs:
      rootDirectory: '$(Build.SourcesDirectory)'
      targetFiles: '**/certificate-metadata.yaml'

  # Apply certificate
  - task: Kubernetes@1
    condition: succeeded()
    displayName: 'Apply Kubernetes certificate'
    inputs:
      connectionType: 'Kubernetes Service Connection'
      kubernetesServiceEndpoint: ${{parameters.serviceConnection}}
      command: apply
      useConfigurationFile: true
      configuration: '$(Build.SourcesDirectory)/certificate-metadata.yaml'
      arguments: '--validate=false'

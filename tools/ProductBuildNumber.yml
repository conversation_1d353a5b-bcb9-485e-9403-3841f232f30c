parameters:
  - name: variableGroupName
    type: string
    default: "ReleaseNumberCount"
  - name: variableCountName
    type: string
    default: "applicationBuildCount"
  - name: useSharedBuildCount
    type: boolean
    default: false

steps:
  - ${{ if or(contains(variables['build.sourceBranch'], 'poc'), eq(variables['build.sourceBranchName'], 'master'), eq(variables['build.sourceBranchName'], 'main')) }}:
      - task: PowerShell@2
        displayName: "Get BuildCounter Value"
        inputs:
          targetType: "inline"
          script: |

            Write-Host "Use Shared Build Count Value: " ${{ parameters.useSharedBuildCount }}

            if ('${{ parameters.useSharedBuildCount }}'.ToString().ToLower() -eq $true.ToString().ToLower())
            {
              Write-Host "Using shared count"
              $GeneratedBuildCounter =  $(${{ parameters.variableCountName }}) + 1
            }
            else
            {
               Write-Host "Using default count"
               $GeneratedBuildCounter = $(BuildCounter)
            }

            Write-Host "##vso[task.setvariable variable=GeneratedBuildCounter]$GeneratedBuildCounter"
      - task: PowerShell@2
        displayName: "Create BuildName"
        inputs:
          targetType: "inline"
          script: |

            function Get-ISOWeekNumber([DateTime] $Date)
            {
                if ($Date.DayOfWeek -ge [DayOfWeek]::Sunday -And $Date.DayOfWeek -le [DayOfWeek]::Wednesday)
                {
                    $Date = $Date.AddDays(3)
                }

                return [System.Globalization.CultureInfo]::InvariantCulture.Calendar.GetWeekOfYear($Date, [System.Globalization.CalendarWeekRule]::FirstFourDayWeek, [DayOfWeek]::Monday)
            }

            function Get-MonthWeekNumber([DateTime] $Date)
            {
                $WeekOfYear = Get-ISOWeekNumber($Date)

                if ($Date.Month -eq 1)
                {
                    return $WeekOfYear
                }

                $FirstDayOfMonth = $Date.AddDays(1 - $Date.Day)
                $FirstDayOfMonthWeekOfYear = Get-ISOWeekNumber($FirstDayOfMonth)
                return $WeekOfYear - $FirstDayOfMonthWeekOfYear + 1

            }

            $Today = Get-Date -Hour 0 -Minute 0 -Second 0
            Write-Host "Today is $Today"

            $ShortYear = $Today.ToString("yy")
            Write-Host "Today short year is $ShortYear"

            $Month = $Today.Month
            Write-Host "Today month is $Month"

            $WeekOfMonth = Get-MonthWeekNumber($Today)
            Write-Host "Today week of month is $WeekOfMonth"

            $ReleaseNumber = "$ShortYear.$Month$WeekOfMonth"
            Write-Host "BLiP release number is $ReleaseNumber"

            $BuildCounter = "$(GeneratedBuildCounter)"
            $BuildNumber = "$ReleaseNumber.$BuildCounter"

            Write-Host "BLiP build number is $BuildNumber"
            Write-Host "##vso[task.setvariable variable=ProductBuildNumber]$BuildNumber"
            Write-Host "##vso[build.updatebuildnumber]$BuildNumber"
      - ${{ if eq(parameters.useSharedBuildCount, true) }}:
          - task: PowerShell@2
            displayName: "Update Shared Build Count"
            inputs:
              targetType: "inline"
              script: |

                Write-Host "Getting Group Id to udpate build counter"
                $groupId = $(az pipelines variable-group list -p $(System.TeamProject) --group-name ${{ parameters.variableGroupName }} --query '[0].id' -o json)

                Write-Host "Updating shared variable in the Variable Group"
                az pipelines variable-group variable update --group-id $groupId --name ${{ parameters.variableCountName }} --value $(GeneratedBuildCounter) --organization "https://dev.azure.com/curupira/" --project $(System.TeamProject)
            env:
              AZURE_DEVOPS_EXT_PAT: $(System.AccessToken)
  - ${{ if contains(variables['build.sourceBranch'], 'hotfix') }}:
      - task: PowerShell@2
        displayName: "Create BuildName Hotfix"
        inputs:
          targetType: "inline"
          script: |
            Write-Host "##vso[task.setvariable variable=ProductBuildNumber]$(build.sourceBranchName)"
            Write-Host "##vso[build.updatebuildnumber]$(build.sourceBranchName)"
  - ${{ if contains(variables['build.sourceBranch'], 'beta') }}:
      - task: PowerShell@2
        displayName: "Create BuildName Beta"
        inputs:
          targetType: "inline"
          script: |
            Write-Host "##vso[task.setvariable variable=ProductBuildNumber]$(build.sourceBranchName)-$(Build.BuildId)"
            Write-Host "##vso[build.updatebuildnumber]$(build.sourceBranchName)-$(Build.BuildId)"

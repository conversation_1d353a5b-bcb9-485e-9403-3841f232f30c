parameters:
  - name: svcConnectionDocker
    type: string
    default: "Take Container Registry-$(System.TeamProject)"

  - name: dockerfiledir
    type: string
    default: "**/Dockerfile"
    displayName: "Dockerfile path. Default is '**/Dockerfile'"

  - name: contextpath
    type: string
    default: "Api"

  - name: tags
    type: string
    default: |
      $(Build.BuildId)
      latest

  - name: repositoryName
    type: string

  - name: dockerArguments
    type: string
    default: ""

  - name: nugetAuthenticate
    type: boolean
    default: false

steps:
  # Check if the build is triggered in "master" or "main" branch or not
  - bash: |
      if [ "$(Build.SourceBranchName)" = "main" ] || [ "$(Build.SourceBranchName)" = "master" ]
      then
        echo "Running on Main Branch - Docker Tags to use: "
        echo "${{ parameters.tags }}" | sed ':a;N;$!ba;s/\n/,/g' | sed 's/,$//'
        parsed_tags=$(echo "${{ parameters.tags }}" | sed ':a;N;$!ba;s/\n/,/g' | sed 's/,$//')
        echo "##vso[task.setvariable variable=dockerTags]$parsed_tags"
      else
        echo "Running on Dev Branch - Docker Tags to use: "
        echo "${{ parameters.tags }}" | sed '/^latest/d' | sed ':a;N;$!ba;s/\n/,/g' | sed 's/,$//'
        parsed_tags=$(echo "${{ parameters.tags }}"| sed '/^latest/d' | sed ':a;N;$!ba;s/\n/,/g' | sed 's/,$//')
        echo "##vso[task.setvariable variable=dockerTags]$parsed_tags"
      fi
    displayName: "Set Docker Tags"

  - ${{ if ne(parameters.dockerArguments, '') }}:
      - task: Docker@2
        displayName: Build An Image
        condition: "ne(variables['Build.Reason'], 'PullRequest')"
        inputs:
          command: build
          containerRegistry: "${{parameters.svcConnectionDocker}}"
          repository: "${{parameters.repositoryName}}"
          Dockerfile: "${{parameters.dockerfiledir}}"
          buildContext: "${{parameters.contextpath}}"
          arguments: "${{parameters.dockerArguments}}"
          tags: "$(dockerTags)"
      - task: Docker@2
        displayName: Push An Image
        condition: "ne(variables['Build.Reason'], 'PullRequest')"
        inputs:
          command: push
          containerRegistry: "${{parameters.svcConnectionDocker}}"
          repository: "${{parameters.repositoryName}}"
          Dockerfile: "${{parameters.dockerfiledir}}"
          buildContext: "${{parameters.contextpath}}"
          tags: "$(dockerTags)"
  - ${{ else }}:
      - task: Docker@2
        displayName: Build and Push An Image
        condition: "ne(variables['Build.Reason'], 'PullRequest')"
        inputs:
          containerRegistry: "${{parameters.svcConnectionDocker}}"
          repository: "${{parameters.repositoryName}}"
          Dockerfile: "${{parameters.dockerfiledir}}"
          buildContext: "${{parameters.contextpath}}"
          tags: "$(dockerTags)"

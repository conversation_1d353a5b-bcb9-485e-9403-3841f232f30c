# Universal Pipeline Template Take Blip

O `UPT Take Blip` é um projeto que consiste na centralização dos pipelines em um só lugar, com isso seu projeto permanece seguro e obedece as boas prática<PERSON>, garantindo `steps` de segurança e qualidade de código.

O Projeto é mantido e atualizado pelo time de `SRE` porém ele é aberto a comunidade `Take Blip` e você pode contribuir a qualquer momento enviando um `Pull Request` com a alteração desejada.

Hoje o Template tem suporte ao [`Azure Pipelines`](https://azure.microsoft.com/pt-br/services/devops/pipelines/) e basta seguir a documentação tanto para migrar ou criar um pipeline novo, clicando [aqui](https://curupira.visualstudio.com/Takepedia/_wiki/wikis/Takepedia.wiki/3366/template).

# Stack suportadas
1.	.Net Core (CI + CD Kubernetes)
2.	Node (CI artefato enviado para o Octopus)
3.	Terraform


parameters:
- name: packagename    
  type: string
  default: ''

- name: chartpath  
  default: ''

- name: configPath
  displayName: 'appsettings.json path'
  type: string
  default: ''

- name: dockerfiledir
  type: string
  default: "**/Dockerfile"
  displayName: "Dockerfile path. Default is '**/Dockerfile'"

- name: contextpath
  type: string
  default: 'Api'

- name: from
  type: string
  default: csps

- name: type
  type: string
  default: dotnet

- name: formatAppSettingsBeforeDelivery
  type: boolean
  default: false

- name: skipSonarBranch
  type: boolean

- name: sonarInclusions
  type: string
  default: '**/*'

- name: sonarExclusions
  type: string
  default: ''

- name: sonarTestInclusions
  type: string
  default: '**/*'  

- name: skipSast
  type: boolean
  default: true
- name: sastFilter
  type: string
  default: '**/*'
- name: scaFilter
  type: string
  default: '**/*'

- name: dockerArguments
  type: string
  default: ''
- name: ServiceConnectionDocker
  type: string
  default: ''
- name: nugetAuthenticate
  type: boolean
  default: false

- name: akvReplaceEscapeType
  type: string
  default: auto

- name: stages
  type: object
  default: []

- name: globalHelm
  type: boolean
  default: false

# .NET parameters

- name: buildConfiguration
  type: string
  default: release

- name: coverageTest
  type: boolean
  default: false

- name: dotnetVersion
  type: string
  default: ''

- name: testParameters
  type: string
  default: '/p:CollectCoverage=true /p:CoverletOutputFormat=opencover /p:CoverletOutput=$(Build.SourcesDirectory)/coverage/'

- name: publishTestResults
  type: string
  default: 'none'

- name: testProjects
  type: string
  default: '**/*Test*.csproj'

- name: vstsFeed
  type: string
  default: ''

- name: useCache
  type: boolean
  default: true

# Javascript parameters

- name: nodeVersion
  type: string
  default: ''
  
- name: npmCmds
  type: object
  default:
    - 'npm cache clean --force'
    - 'npm install'
    - 'npm run build'

- name: configurationFromEnvFile
  type: boolean
  default: false

jobs:
- job: "build"  
  displayName: "Build a Project Package"
  variables: 
  - template: ../configuration/config.yaml
    parameters: 
      deployCluster: kubernetes
      from: ${{ parameters.from }}
  
  - ${{ if eq(parameters.formatAppSettingsBeforeDelivery, true) }}:
    - ${{ each stage in parameters.stages  }}:
      - ${{ if stage.condition }}:
        - ${{ each variable in stage.variables }}:
          - group: ${{ variable }}
  steps:
  - checkout: self
    persistCredentials: true
    fetchDepth: 0
  - bash: |        
      if [ '${{ parameters.chartpath }}' = '' ]
      then
        chartpath=$(find . -type d -iname 'charts')         
        echo "##vso[task.setvariable variable=chart]$chartpath"
      else
        echo "##vso[task.setvariable variable=chart]${{ parameters.chartpath }}"
      fi      
    displayName: 'Set Chart Path'
  - bash: |        
      if [ '${{ parameters.configPath }}' = '' ]
      then
        settings=$(find . -type f -iname 'appsettings.json') 
        echo "##vso[task.setvariable variable=settings]$settings"
      else
        echo "##vso[task.setvariable variable=settings]${{ parameters.configPath }}"
      fi      
    displayName: 'Set Settings Path'
  - bash: |
      if [ "$(Build.SourceBranchName)" = "main" ] || [ "$(Build.SourceBranchName)" = "master" ];
      then
        echo "##vso[task.setvariable variable=imagename]${{ parameters.packagename }}-prod"        
      else
        echo "##vso[task.setvariable variable=imagename]${{ parameters.packagename }}"
      fi
    displayName: 'Create Name Image'
  - ${{ if eq(parameters.type, 'dotnet') }}:
    - template: ../dotnet/dotnet-commands.yml
      parameters:    
        buildConfiguration: ${{parameters.buildConfiguration}}       
        coverageTest: ${{parameters.coverageTest}}
        dotnetVersion: ${{ parameters.dotnetVersion }}
        testParameters: ${{ parameters.testParameters }}               
        testProjects: ${{ parameters.testProjects }}
        vstsFeed: ${{ parameters.vstsFeed }}
        skipSonarBranch: ${{ parameters.skipSonarBranch }}
        sonarInclusions: ${{parameters.sonarInclusions}}
        sonarExclusions: ${{parameters.sonarExclusions}}
        skipSast: ${{ parameters.skipSast }}
        sastFilter: ${{ parameters.sastFilter }}
        scaFilter: ${{ parameters.scaFilter }}
        nugetAuthenticate: ${{ parameters.nugetAuthenticate }}
        publishTestResults: ${{ parameters.publishTestResults }}
        useCache: ${{ parameters.useCache }}
  - ${{ if eq(parameters.type, 'javascript') }}:
    - template: ../javascript/npm-commands.yml
      parameters:    
        nodeVersion: ${{ parameters.nodeVersion }}
        npmCmds: ${{ parameters.npmCmds }}
        skipSonarBranch: ${{ parameters.skipSonarBranch }}
        sonarInclusions: ${{parameters.sonarInclusions}}
        sonarExclusions: ${{parameters.sonarExclusions}}
        sonarTestInclusions: ${{ parameters.sonarTestInclusions }}
        skipSast: ${{ parameters.skipSast }}
        sastFilter: ${{ parameters.sastFilter }}
        scaFilter: ${{ parameters.scaFilter }}
    - ${{ if eq(parameters.formatAppSettingsBeforeDelivery, true) }}:
      - task: qetza.replacetokens.replacetokens-task.replacetokens@3
        displayName: 'Replace Tokens **/*appsettings.json'
        inputs:
          targetFiles: '**/*appsettings.json'
          writeBOM: false
          escapeType: ${{ parameters.akvReplaceEscapeType }}
  - template: ../docker/template-ci.yml
    parameters:
      ${{ if eq(parameters.ServiceConnectionDocker, '') }}:
        svcConnectionDocker: 'Take Container Registry-$(System.TeamProject)'
      ${{ else }}:
        svcConnectionDocker: ${{ parameters.ServiceConnectionDocker }}
      dockerfiledir: '${{parameters.dockerfiledir}}'
      contextpath: '${{parameters.contextpath}}' 
      repositoryName: '$(imagename)'
      ${{ if eq(parameters.nugetAuthenticate, false)  }}:
        dockerArguments: '${{parameters.dockerArguments}}'
      ${{ else  }}:
        dockerArguments: '${{parameters.dockerArguments}} --build-arg ACCESS_TOKEN=$(VSS_NUGET_ACCESSTOKEN)'
  - task: PublishBuildArtifacts@1
    displayName: Publish AppSettings
    inputs:
      PathtoPublish: $(settings)
      ArtifactName: ApiAppsettings
  - ${{ if eq(parameters.globalHelm, true) }}:
    - task: PublishBuildArtifacts@1
      displayName: Publish Helm Values
      inputs:
        PathtoPublish: $(Build.SourcesDirectory)/values.yaml
        ArtifactName: HelmValues
  - ${{ else }}:
    - task: HelmInstaller@0
      displayName: Install Helm
      inputs:
        helmVersion: $(namehelmVersion)
        checkLatestHelmVersion: false
    - task: Bash@3
      displayName: Helm Prepare Chart
      inputs:
        targetType: inline
        script: >
          export HELM_EXPERIMENTAL_OCI=1


          echo $(registryPassword) |  helm registry login $(registryName).azurecr.io --username $(registryLogin) --password-stdin


          helm chart save $(chart)/${{ parameters.packagename }} $(registryName).azurecr.io/helm/${{ parameters.packagename }}:v$(build.buildId)


          helm chart push $(registryName).azurecr.io/helm/${{ parameters.packagename }}:v$(build.buildId)
  - ${{ if eq(parameters.configurationFromEnvFile, true) }}:
    - task: PublishBuildArtifacts@1
      displayName: Publish Env File
      inputs:
         PathtoPublish: $(Build.SourcesDirectory)/.env.adjusted
         ArtifactName: EnvValues
parameters:
- name: packagename
  displayName: 'Name of the Project'
  default: ''
  type: string
- name: chartPath
  displayName: 'The chart path'
  type: string
  default: ''
- name: environment
  type: string  
  displayName: "Environment"
- name: serviceConnection
  type: string  
  displayName: "Service Connection Name - Deploy"
- name: variables
  type: object
  default: []
  displayName: "Variables Stage"
- name: from
  type: string
- name: helmCustomParameters
  type: string
  default: ''
- name: akvReplaceEscapeType
  type: string
  default: auto
- name: shouldAddAlarm
  type: boolean
  default: false
- name: shouldAddApiStatusAlarm
  type: boolean
  default: true
- name: shouldAddApiErrorsAlarm
  type: boolean
  default: true
- name: shouldAddBotStatusAlarm
  type: boolean
  default: true
- name: shouldAddBotErrorsAlarm
  type: boolean
  default: true
- name: applyKubernetesCertificate
  type: boolean
  default: false
- name: formatAppSettingsBeforeDelivery
  type: boolean
  default: false
- name: globalHelm
  type: boolean
  default: false
- name: chartVersion
  type: string
  default: ''
- name: namespace
  type: string
  default: 'default'
- name: configurationFromEnvFile
  type: boolean
  default: false

jobs:
- deployment: cd_${{parameters.environment}}
  environment: ${{ parameters.environment }}
  variables: 
  - template: ../configuration/config.yaml
    parameters: 
      deployCluster: kubernetes
      from: ${{ parameters.from }}           
      environment: ${{ parameters.environment }}
  - ${{ each variable in parameters.variables  }}: 
    - group: ${{ variable }}     
  strategy:
    runOnce:
      deploy:
        steps:
        - bash: |
            if [ "$(Build.SourceBranchName)" = "main" ] || [ "$(Build.SourceBranchName)" = "master" ]
            then
              echo "##vso[task.setvariable variable=imagename]${{parameters.packagename}}-prod"
            else
              echo "##vso[task.setvariable variable=imagename]${{parameters.packagename}}"
            fi
          displayName: 'Create Name Image'
        - bash: |        
            if [ '${{ parameters.chartPath }}' = '' ]
            then
              chartPath=$(find . -type d -iname 'charts')         
              echo "##vso[task.setvariable variable=chart]$chartPath"
            else
              echo "##vso[task.setvariable variable=chart]${{ parameters.chartPath }}"
            fi      
          displayName: 'Set Chart Name'
        - bash: |        
            if [ '${{ parameters.packagename }}' = '' ]
            then
              projectName=$(ls $(chart))         
              echo "##vso[task.setvariable variable=projectName]$projectName"
            else
              echo "##vso[task.setvariable variable=projectName]${{ parameters.packagename }}"
            fi      
          displayName: 'Set Project Path'
        - task: DownloadBuildArtifacts@0
          inputs:
            buildType: 'current'
            downloadType: 'single'
            artifactName: 'ApiAppsettings'
            downloadPath: '$(System.DefaultWorkingDirectory)'
        - ${{if eq(parameters.formatAppSettingsBeforeDelivery, false) }}:
            - task: FileTransform@1
              displayName: 'File Transform AppSettings'
              inputs:
                folderPath: '$(System.DefaultWorkingDirectory)/ApiAppsettings/'
                fileType: json
                targetFiles: appsettings.json
            - task: qetza.replacetokens.replacetokens-task.replacetokens@3
              displayName: 'Replace Tokens **/*appsettings.json'
              inputs:
                targetFiles: '**/*appsettings.json'
                writeBOM: false
                escapeType: ${{ parameters.akvReplaceEscapeType }}
        - task: Kubernetes@1
          displayName: 'Kubernetes Login'
          inputs:
            connectionType: 'Kubernetes Service Connection'
            kubernetesServiceEndpoint: ${{parameters.serviceConnection}}
            command: 'login'
        - script: |
            if kubectl get namespace "$namespace" > /dev/null 2>&1; then
              echo "##vso[task.setvariable variable=namespaceExists;]true"
            else
              echo "##vso[task.setvariable variable=namespaceExists;]false"
            fi
          env:
            namespace: ${{parameters.namespace}}
          displayName: 'Verify if Namespace Exists'    
        - script: |
            if [ "$(namespaceExists)" = "false" ]; then
              kubectl create namespace "$namespace"
            fi
          env:
            namespace: ${{parameters.namespace}}
          displayName: 'Create Namespace'    
        - script: |
            if [ "$(namespaceExists)" = "false" ]; then
              kubectl create secret docker-registry $(acrSecretName) \
                -n "$namespace" \
                --docker-server=$(registryName).azurecr.io \
                --docker-username=$(registryLogin) \
                --docker-password=$(registryPassword)
            fi
          env:
            namespace: ${{parameters.namespace}}
          displayName: 'Create Image Pull Secret'  
        - ${{ if eq(parameters.globalHelm, true) }}:    
          - task: DownloadBuildArtifacts@0
            inputs:
              buildType: 'current'
              downloadType: 'single'
              artifactName: 'HelmValues'
              downloadPath: '$(System.DefaultWorkingDirectory)'
          - task: qetza.replacetokens.replacetokens-task.replacetokens@6
            displayName: 'Replace Tokens values.yaml'
            inputs:
              sources: '$(System.DefaultWorkingDirectory)/HelmValues/values.yaml'
              escape: ${{ parameters.akvReplaceEscapeType }}
              tokenPattern: 'azpipelines'
              additionalVariables: |
                - environment: ${{ parameters.environment }}  
          - bash: cat $(System.DefaultWorkingDirectory)/HelmValues/values.yaml
            displayName: 'Show Helm Values'
          - task: HelmInstaller@1
            displayName: 'Install Helm'
            inputs:
              helmVersionToInstall: '3.15.3'
        - ${{ else }}:
          - ${{ if eq(parameters.configurationFromEnvFile, true) }}:
            - task: DownloadBuildArtifacts@0
              inputs:
                buildType: 'current'
                downloadType: 'single'
                artifactName: 'EnvValues'
                downloadPath: '$(System.DefaultWorkingDirectory)'
            - task: qetza.replacetokens.replacetokens-task.replacetokens@6
              displayName: 'Replace Token on .env File'
              inputs: 
                sources: '$(System.DefaultWorkingDirectory)/EnvValues/.env.adjusted'
                escape: ${{ parameters.akvReplaceEscapeType }}
                tokenPattern: 'default'
            - bash: cat $(System.DefaultWorkingDirectory)/EnvValues/.env.adjusted
              displayName: 'Show Env Values'
            - task: Kubernetes@1
              displayName: 'Create Configmap from Env File'
              inputs:
                connectionType: 'Kubernetes Service Connection'
                kubernetesServiceEndpoint: ${{parameters.serviceConnection}}
                namespace: ${{parameters.namespace}}
                forceUpdateConfigMap: true
                configMapName: '$(projectName)-${{ parameters.environment}}'
                configMapArguments: --from-env-file=$(System.DefaultWorkingDirectory)/EnvValues/.env.adjusted
          - ${{ else }}:
            - task: Kubernetes@1
              displayName: 'Create Configmap'
              inputs:
                connectionType: 'Kubernetes Service Connection'
                kubernetesServiceEndpoint: ${{parameters.serviceConnection}}
                namespace: ${{parameters.namespace}}                
                configMapName: '$(projectName)-${{ parameters.environment}}'
                forceUpdateConfigMap: true
                useConfigMapFile: true
                configMapFile: '$(System.DefaultWorkingDirectory)/ApiAppsettings/appsettings.json'    
          - task: HelmInstaller@0
            displayName: 'Install Helm'
            inputs:
              helmVersion: '$(namehelmVersion)'
              checkLatestHelmVersion: false          
        - bash: 'helm repo add $(registryName) https://$(registryName).azurecr.io/helm/v1/repo --username $(registryLogin) --password $(registryPassword) && helm repo update' 
          displayName: 'Helm Repo Add' 
        - ${{ if eq(parameters.globalHelm, false) }}:
          - bash: |
              export HELM_EXPERIMENTAL_OCI=1
        
              echo $(registryPassword) |  helm registry login $(registryName).azurecr.io --username $(registryLogin) --password-stdin
              helm chart pull $(registryName).azurecr.io/helm/$(projectName):v$(build.buildId)
              helm chart export  $(registryName).azurecr.io/helm/$(projectName):v$(build.buildId)  --destination ./install
            displayName: 'Helm Prepare Chart'     

          - ${{ if eq(parameters.configurationFromEnvFile, false) }}:
            - task: HelmDeploy@0
              displayName: 'Helm Charts Upgrade'
              inputs:
                connectionType: 'Kubernetes Service Connection'
                kubernetesServiceConnection: ${{parameters.serviceConnection}}
                command: upgrade
                namespace: ${{parameters.namespace}}
                chartName: './install/$(projectName)'
                releaseName: '$(projectName)-${{ parameters.environment }}'
                ${{ if eq(variables['secretName'], '') }}:
                  arguments: '--set image.repository=$(registryName).azurecr.io/$(imagename) --set image.tag=$(build.buildId) --set appSettings.configMap="$(projectName)-${{ parameters.environment }}" --set image.imagePullSecretName="$(acrSecretName)"  --set ingress.hostName="$(projectName)$(hostName)" --set environment.stage="${{ parameters.environment }}" --set autoscale.minReplicas="$(minReplicas)" --set autoscale.maxReplicas="$(maxReplicas)" --set autoscale.averageCPUUtilization="$(averageCpuUtilization)" --set resources.limits.cpu="$(limitCpu)" --set resources.limits.memory="$(limitMemory)" --set resources.requests.cpu="$(requestsCpu)" --set resources.requests.memory="$(requestsMemory)" --set team="$(team)" ${{parameters.helmCustomParameters}}'
                ${{ else }}:
                  arguments: '--version $(build.buildId) --set image.repository=$(registryName).azurecr.io/$(imagename) --set image.tag=$(build.buildId) --set appSettings.configMap="$(projectName)-${{ parameters.environment }}" --set image.imagePullSecretName="$(acrSecretName)"  --set ingress.hostName="$(projectName)$(hostName)" --set environment.stage="${{ parameters.environment }}" --set ingress.tls.secretName="$(secretName)" --set autoscale.minReplicas="$(minReplicas)" --set autoscale.maxReplicas="$(maxReplicas)" --set autoscale.averageCPUUtilization="$(averageCpuUtilization)" --set resources.limits.cpu="$(limitCpu)" --set resources.limits.memory="$(limitMemory)" --set resources.requests.cpu="$(requestsCpu)" --set resources.requests.memory="$(requestsMemory)" --set team="$(team)" ${{parameters.helmCustomParameters}}'
              
                failOnStderr: true
          - ${{ else }}:          
            - task: HelmDeploy@0
              displayName: 'Helm Charts Upgrade with Env Parameter'
              inputs:
                connectionType: 'Kubernetes Service Connection'
                kubernetesServiceConnection: ${{parameters.serviceConnection}}
                command: upgrade
                namespace: ${{parameters.namespace}}
                chartName: './install/$(projectName)'
                releaseName: '$(projectName)-${{ parameters.environment }}'
                ${{ if eq(variables['secretName'], '') }}:
                  arguments: '--set env.configMapName="$(projectName)-${{ parameters.environment }}" --set image.repository=$(registryName).azurecr.io/$(imagename) --set image.tag=$(build.buildId) --set appSettings.configMap="$(projectName)-${{ parameters.environment }}" --set image.imagePullSecretName="$(acrSecretName)"  --set ingress.hostName="$(projectName)$(hostName)" --set environment.stage="${{ parameters.environment }}" --set autoscale.minReplicas="$(minReplicas)" --set autoscale.maxReplicas="$(maxReplicas)" --set autoscale.averageCPUUtilization="$(averageCpuUtilization)" --set resources.limits.cpu="$(limitCpu)" --set resources.limits.memory="$(limitMemory)" --set resources.requests.cpu="$(requestsCpu)" --set resources.requests.memory="$(requestsMemory)" --set team="$(team)" ${{parameters.helmCustomParameters}}'
                ${{ else }}:
                  arguments: '--set env.configMapName="$(projectName)-${{ parameters.environment }}" --version $(build.buildId) --set image.repository=$(registryName).azurecr.io/$(imagename) --set image.tag=$(build.buildId) --set appSettings.configMap="$(projectName)-${{ parameters.environment }}" --set image.imagePullSecretName="$(acrSecretName)"  --set ingress.hostName="$(projectName)$(hostName)" --set environment.stage="${{ parameters.environment }}" --set ingress.tls.secretName="$(secretName)" --set autoscale.minReplicas="$(minReplicas)" --set autoscale.maxReplicas="$(maxReplicas)" --set autoscale.averageCPUUtilization="$(averageCpuUtilization)" --set resources.limits.cpu="$(limitCpu)" --set resources.limits.memory="$(limitMemory)" --set resources.requests.cpu="$(requestsCpu)" --set resources.requests.memory="$(requestsMemory)" --set team="$(team)" ${{parameters.helmCustomParameters}}'
              
                failOnStderr: true
          - ${{ if eq(parameters.applyKubernetesCertificate, true) }}:
            - template: '../tools/ApplyCertificateMetadata.yml'
              parameters:
                serviceConnection: ${{ parameters.serviceConnection }}
        - ${{ else }}:
          - ${{ if eq(parameters.chartVersion, '') }}:
              - bash: |
                  export HELM_EXPERIMENTAL_OCI=1
            
                  echo $(registryPassword) |  helm registry login $(registryName).azurecr.io --username $(registryLogin) --password-stdin
                  helm pull oci://$(registryName).azurecr.io/helm/$(chartName) --untar --untardir ./install
                displayName: 'Helm Prepare Chart'
          - ${{ else }}:
              - bash: |
                  export HELM_EXPERIMENTAL_OCI=1
            
                  echo $(registryPassword) |  helm registry login $(registryName).azurecr.io --username $(registryLogin) --password-stdin
                  helm pull oci://$(registryName).azurecr.io/helm/$(chartName) --version ${{ parameters.chartVersion }} --untar --untardir ./install
                displayName: 'Helm Prepare Chart'
          - task: HelmDeploy@0
            displayName: 'Helm Charts Upgrade'
            inputs:
              connectionType: 'Kubernetes Service Connection'
              kubernetesServiceConnection: ${{parameters.serviceConnection}}
              command: upgrade
              namespace: ${{parameters.namespace}}
              chartName: './install/$(chartName)'
              releaseName: '$(projectName)-${{ parameters.environment }}'
              ${{ if eq(parameters.chartVersion, '') }}:
                arguments: >-
                  -f $(System.DefaultWorkingDirectory)/HelmValues/values.yaml
                  ${{parameters.helmCustomParameters}} 
                  --set-file appsettings=$(System.DefaultWorkingDirectory)/ApiAppsettings/appsettings.json
                  --create-namespace
              ${{ else }}:
                arguments: >-
                  -f $(System.DefaultWorkingDirectory)/HelmValues/values.yaml
                  --version ${{ parameters.chartVersion }} 
                  ${{parameters.helmCustomParameters}} 
                  --set-file appsettings=$(System.DefaultWorkingDirectory)/ApiAppsettings/appsettings.json
                  --create-namespace
              failOnStderr: true
        - powershell:  |
            $nagiosHostName = '$(nagiosHostName)'
            $apiBaseEndpoint = 'https://$(projectName)$(hostName)'
            $checkState = "Check Status API - $apiBaseEndpoint"

            $checkCommand = "check_https_string!$(projectName)$(hostName)!$(healthEndpoint)!Healthy"

            Write-Host $checkCommand

            $Uri = "https://take.nebrasil.com.br/nagiosxi/api/v1/config/service?apikey=$(nagios-ApiToken)"

            try {
                $formData = @{
                            use = 'generic-service';
                            host_name = $nagiosHostName;
                            service_description = $checkState;
                            check_command = $checkCommand;
                            check_interval = '10';
                            retry_interval = '2';
                            max_check_attempts = '2';
                            check_period = '24x7';
                            contact_groups = 'servicedesk-group,suporte-group,smartcontacts-group';
                            notification_interval = '120';
                            notification_period = '24x7';
                            notes = 'Criado Automaticamente. This alarm is from responsibility of Celula $(team). $(documentationLink)';
                            }

                Invoke-WebRequest -UseBasicParsing -Uri $Uri -Method Post -Body $formData -ContentType "application/x-www-form-urlencoded"

                Write-Host "Created Nagios alarm for $(projectName)"
            }
            catch {
                $ErrorMessage = $_.Exception.Message
                Write-Error $ErrorMessage
            }
          displayName: 'Add HealthCheck Alarm'
          condition: and(succeeded(), eq(${{parameters.shouldAddAlarm}}, true), eq(${{parameters.shouldAddApiStatusAlarm}}, true), ne('$(documentationLink)',''), ne('$(documentationLink)','`$(documentationLink)'))
        - powershell:  |
            $nagiosHostName = '$(nagiosHostName)'
            $apiBaseEndpoint = 'https://$(projectName)$(hostName)'
            $checkState = "Check Errors SEQ API - $apiBaseEndpoint"
            $applicationLogName = '$(applicationLogName)';
            $query = "@Level = 'Error' and Application = '$applicationLogName'"

            $checkCommand = "check_seq_bots!`"$query`" 5 15!!!!!!!"

            Write-Host $checkCommand

            $Uri = "https://take.nebrasil.com.br/nagiosxi/api/v1/config/service?apikey=$(nagios-ApiToken)"

            try {
                $formData = @{
                            use = 'generic-service';
                            host_name = $nagiosHostName;
                            service_description = $checkState;
                            check_command = $checkCommand;
                            check_interval = '10';
                            retry_interval = '2';
                            max_check_attempts = '2';
                            check_period = '24x7';
                            contact_groups = 'servicedesk-group,suporte-group,smartcontacts-group';
                            notification_interval = '120';
                            notification_period = '24x7';
                            notes = 'Criado Automaticamente. This alarm is from responsibility of Celula $(team). $(documentationLink)';
                            }

                Invoke-WebRequest -UseBasicParsing -Uri $Uri -Method Post -Body $formData -ContentType "application/x-www-form-urlencoded"

                Write-Host "Created Nagios alarm for $(projectName)"
            }
            catch {
                $ErrorMessage = $_.Exception.Message
                Write-Error $ErrorMessage
            }
          displayName: 'Add API Errors Alarm'
          condition: and(succeeded(), eq(${{parameters.shouldAddAlarm}}, true), eq(${{parameters.shouldAddApiErrorsAlarm}}, true), ne('$(documentationLink)',''), ne('$(documentationLink)','`$(documentationLink)'))
        - powershell:  |
            $nagiosHostName = '$(nagiosHostName)'
            $organization = '$(botOrganization)'
            $botIdentifiers = '$(botIdentifiers)'
            $botIdentifyArray = $botIdentifiers.Split(',')

            Write-Host Bots to create alarm organization: $organization  bots: $botIdentifiers

            foreach($bot in $botIdentifyArray)
            {
              Write-Host Creating alarm for $bot

              $checkState = "Check Bot Online - $bot"

              $checkCommand = "check_http_string_bot!admin.msging.net!'/probes/ping?sender=probe%40msging.net&senderPassword=S21WMzRDUGxqR1ByemN4&receiver=$<EMAIL>&senderServerHostname=$organization&senderServerPort=443'!'\`"ok`"'!!!!!"

              Write-Host $checkCommand

              $Uri = "https://take.nebrasil.com.br/nagiosxi/api/v1/config/service?apikey=$(nagios-ApiToken)"

              try {
                  $formData = @{
                              use = 'generic-service';
                              host_name = $nagiosHostName;
                              service_description = $checkState;
                              check_command = $checkCommand;
                              check_interval = '10';
                              retry_interval = '2';
                              max_check_attempts = '2';
                              check_period = '24x7';
                              contact_groups = 'servicedesk-group,suporte-group,smartcontacts-group';
                              notification_interval = '120';
                              notification_period = '24x7';
                              notes = 'Criado Automaticamente. This alarm is from responsibility of Celula $(team). $(documentationLink)';
                              }

                  Invoke-WebRequest -UseBasicParsing -Uri $Uri -Method Post -Body $formData -ContentType "application/x-www-form-urlencoded"

                  Write-Host "Created Nagios alarm for $<EMAIL>"
              }
              catch {
                  $ErrorMessage = $_.Exception.Message
                  Write-Error $ErrorMessage
              }
            }
          displayName: 'Add Bot Online Alarm'
          condition: and(succeeded(), eq(${{parameters.shouldAddAlarm}}, true), eq(${{parameters.shouldAddBotStatusAlarm}}, true), ne('$(documentationLink)',''), ne('$(documentationLink)','`$(documentationLink)'), ne('$(botIdentifiers)','`$(botIdentifiers)'), ne('$(botIdentifiers)',''), ne('$(botOrganization)',''), ne('$(botOrganization)','`$(botOrganization)'))
        - powershell:  |
            $nagiosHostName = '$(nagiosHostName)'
            $organization = '$(botOrganization)'
            $botIdentifiers = '$(botIdentifiers)'
            $botIdentifyArray = $botIdentifiers.Split(',')

            Write-Host Bots to create alarm organization: $organization  bots: $botIdentifiers

            foreach($bot in $botIdentifyArray)
            {
              Write-Host Creating alarm for $bot

              $checkState = "Check Bot Errors -$bot"

              $query = "@Level = 'Warning' and To = '$<EMAIL>'"

              $checkCommand = "check_seq_blip!`"$query`" 5 15!!!!!!!"

              Write-Host $checkCommand

              $Uri = "https://take.nebrasil.com.br/nagiosxi/api/v1/config/service?apikey=$(nagios-ApiToken)"

              try {
                  $formData = @{
                              use = 'generic-service';
                              host_name = $nagiosHostName;
                              service_description = $checkState;
                              check_command = $checkCommand;
                              check_interval = '10';
                              retry_interval = '2';
                              max_check_attempts = '2';
                              check_period = '24x7';
                              contact_groups = 'servicedesk-group,suporte-group,smartcontacts-group';
                              notification_interval = '120';
                              notification_period = '24x7';
                              notes = 'Criado Automaticamente. This alarm is from responsibility of Celula $(team). $(documentationLink)';
                              }

                  Invoke-WebRequest -UseBasicParsing -Uri $Uri -Method Post -Body $formData -ContentType "application/x-www-form-urlencoded"

                  Write-Host "Created Nagios alarm for $bot"
              }
              catch {
                  $ErrorMessage = $_.Exception.Message
                  Write-Error $ErrorMessage
              }
            }
          displayName: 'Add Bot Errors SEQ Alarm'
          condition: and(succeeded(), eq(${{parameters.shouldAddAlarm}}, true), eq(${{parameters.shouldAddBotErrorsAlarm}}, true), ne('$(documentationLink)',''), ne('$(documentationLink)','`$(documentationLink)'), ne('$(botIdentifiers)','`$(botIdentifiers)'), ne('$(botIdentifiers)',''))
parameters:
  - name: rubyVersion
    type: string
    default: '2.4.x' 
  - name: rubyCmds
    type: object
    default: ''
  - name: skipSonarBranch
    type: boolean 

jobs:
  - job: Test
    steps:
    - template: ruby-commands.yml
      parameters:        
        rubyVersion: ${{ parameters.rubyVersion }}
        rubyCmds: ${{ parameters.rubyCmds }}
        skipSonarBranch: ${{ parameters.skipSonarBranch }}


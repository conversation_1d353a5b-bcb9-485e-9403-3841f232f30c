parameters:
  - name: rubyVersion
    type: string
    default: '2.4.x' 
  - name: rubyCmds
    type: object
    default: ''
  - name: skipSonarBranch
    type: boolean 

steps:
  - ${{ if eq(parameters.skipSonarBranch, false) }}:
    - template: ../si/pre-steps-template.yml
      parameters:
        type: ruby
  - task: UseRubyVersion@0
    inputs:
      versionSpec: '${{ parameters.rubyVersion }}'
  - ${{ each cmd in parameters.rubyCmds }}:
    - task: PowerShell@2
      condition: succeeded()
      displayName: '${{ cmd }}'
      inputs:
        targetType: "inline"
        script: |
          Invoke-Expression "${{ cmd }}"
  - ${{ if eq(parameters.skipSonarBranch, false) }}:
    - template: ../si/pos-steps-template.yml
parameters:
- name: sastFilter
  type: string
  default: '**/*'
- name: scaFilter
  type: string
  default: '**/*'

steps:
  - task: Checkmarx AST@2
    inputs:
      CheckmarxService: 'Checkmarx AST'
      projectName: '$(checkmarxName)'
      branchName: '$(Build.SourceBranchName)'
      tenantName: 'takeblip'
      additionalParams: '--async --sast-filter "${{parameters.sastFilter}}" --sca-filter "${{parameters.scaFilter}}" '
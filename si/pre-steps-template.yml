parameters:
- name: sonarInclusions
  type: string
  default: '**/*'
- name: sonarExclusions
  type: string
  default: ''
- name: sonarExecutionReportPaths
  type: string
  default: ''
- name: type
  default: dotnet
  type: string
- name: sonarTestInclusions
  type: string
  default: '**/*'

steps:
- ${{ if and(eq(variables['build.sourceBranchName'], 'master'), eq(variables['build.sourceBranchName'], 'main')) }}:
  - ${{ if ne(parameters.preSolution, '') }}:
    - task: PowerShell@2
      displayName: 'SI: Verificar Tag para Sonarqube'
      inputs:
        targetType: 'inline'
        script: 'if ([string]::IsNullOrWhitespace($env:SONARQUBETAGS)) { throw "Não foi fornecida a Tag para integração Sonarqube" }'
    - task: PowerShell@2
      displayName: 'SI: Verificar Framework para Sonarqube'
      inputs:
        targetType: 'inline'
        script: 'if ([string]::IsNullOrWhitespace($env:FRAMEWORK)) { throw "Não foi fornecido framework para integração Sonarqube" }'
    - task: SonarQubePrepare@7
      displayName: 'SI: Prepare Code Analysis (Others)'
      inputs:
        SonarQube: 'SonarQube - Tools'
        scannerMode: CLI
        configMode: manual
        cliProjectKey: '$(sonarQubeKey)'
        cliProjectName: '$(sonarQubeName)'
        extraProperties: |
            sonar.cs.opencover.reportsPaths=$(Build.SourcesDirectory)/**/coverage.opencover.xml
            sonar.exclusions=${{parameters.sonarExclusions}},**/coverage.opencover.xml
            sonar.inclusions=${{parameters.sonarInclusions}}
            sonar.test.inclusions=${{parameters.sonarTestInclusions}}
      condition: and(ne(variables['framework'], 'dotnet'), ne(variables['framework'], 'javascript'), not(contains(variables['Build.Repository.Name'], 'broad')))
      continueOnError: true
    - task: SonarQubePrepare@7
      displayName: 'SI: Prepare Code Analysis (.NET)'
      inputs:
        SonarQube: 'SonarQube - Tools'
        projectKey: '$(sonarQubeKey)'
        projectName: '$(sonarQubeName)'
        extraProperties: |
            sonar.cs.opencover.reportsPaths=$(Build.SourcesDirectory)/**/coverage.opencover.xml
            sonar.exclusions=${{parameters.sonarExclusions}},**/coverage.opencover.xml
            sonar.inclusions=${{parameters.sonarInclusions}}
            sonar.test.inclusions=${{parameters.sonarTestInclusions}}            
      condition: eq(variables['framework'], 'dotnet')
      continueOnError: true
    - task: SonarQubePrepare@7
      displayName: 'SI: Prepare Code Analysis (JS)'
      inputs:
        SonarQube: 'SonarQube - Tools'
        scannerMode: CLI
        configMode: manual
        cliProjectKey: '$(sonarQubeKey)'
        cliProjectName: '$(sonarQubeName)'
        extraProperties: |
            sonar.javascript.lcov.reportPaths=coverage/lcov.info
            sonar.testExecutionReportPaths=${{ parameters.sonarExecutionReportPaths }}
            sonar.tests=src
            sonar.test.inclusions=**/*.test.js
            sonar.exclusions=${{parameters.sonarExclusions}},**/coverage,**/*.spec.*,**/*.test.*,/*.config.*
            sonar.inclusions=${{parameters.sonarInclusions}}
      condition: eq(variables['framework'], 'javascript')
      continueOnError: true
- ${{ else }}:
  - task: PowerShell@2
    displayName: 'SI: Verificar Tag para Sonarqube'
    inputs:
      targetType: 'inline'
      script: 'if ([string]::IsNullOrWhitespace($env:SONARQUBETAGS)) { throw "Não foi fornecida a Tag para integração Sonarqube" }'
  - task: PowerShell@2
    displayName: 'SI: Verificar Framework para Sonarqube'
    inputs:
      targetType: 'inline'
      script: 'if ([string]::IsNullOrWhitespace($env:FRAMEWORK)) { throw "Não foi fornecido framework para integração Sonarqube" }'
  - task: SonarQubePrepare@7
    displayName: 'SI: Prepare Code Analysis (Others)'
    inputs:
      SonarQube: 'SonarQube - Tools'
      scannerMode: CLI
      configMode: manual
      cliProjectKey: '$(sonarQubeKey)'
      cliProjectName: '$(sonarQubeName)'
      extraProperties: |
          sonar.cs.opencover.reportsPaths=$(Build.SourcesDirectory)/**/coverage.opencover.xml
          sonar.exclusions=${{parameters.sonarExclusions}},**/coverage.opencover.xml
          sonar.inclusions=${{parameters.sonarInclusions}}
          sonar.test.inclusions=${{parameters.sonarTestInclusions}}          
    condition: and(ne(variables['framework'], 'dotnet'), ne(variables['framework'], 'javascript'),not(contains(variables['Build.Repository.Name'], 'broad')))
    continueOnError: true
  - task: SonarQubePrepare@7
    displayName: 'SI: Prepare Code Analysis (.NET)'
    inputs:
      SonarQube: 'SonarQube - Tools'
      projectKey: '$(sonarQubeKey)'
      projectName: '$(sonarQubeName)'
      extraProperties: |
          sonar.cs.opencover.reportsPaths=$(Build.SourcesDirectory)/**/coverage.opencover.xml
          sonar.exclusions=${{parameters.sonarExclusions}},**/coverage.opencover.xml
          sonar.inclusions=${{parameters.sonarInclusions}}
          sonar.test.inclusions=${{parameters.sonarTestInclusions}}
    condition: eq(variables['framework'], 'dotnet')
    continueOnError: true
  - task: SonarQubePrepare@7
    displayName: 'SI: Prepare Code Analysis (JS)'
    inputs:
      SonarQube: 'SonarQube - Tools'
      scannerMode: CLI
      configMode: manual
      cliProjectKey: '$(sonarQubeKey)'
      cliProjectName: '$(sonarQubeName)'
      extraProperties: |
          sonar.javascript.lcov.reportPaths=coverage/lcov.info
          sonar.testExecutionReportPaths=${{ parameters.sonarExecutionReportPaths }}
          sonar.tests=src
          sonar.test.inclusions=**/*.test.js
          sonar.exclusions=${{parameters.sonarExclusions}},**/coverage,**/*.spec.*,**/*.test.*,/*.config.*
          sonar.inclusions=${{parameters.sonarInclusions}}
    condition: eq(variables['framework'], 'javascript')
    continueOnError: true
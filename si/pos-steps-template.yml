parameters:
- name: SonarAzureToken
  type: string
  default: squ_f99f36e7a0395baa6930bc2c6b857df3687a600f

steps:
- ${{ if and(eq(variables['build.sourceBranchName'], 'master'), eq(variables['build.sourceBranchName'], 'main')) }}:
  - ${{ if ne(parameters.preSolution, '') }}:
    - task: SonarQubeAnalyze@7
      displayName: 'SI: Run Code Analysis'
      inputs:
        SonarQube: 'SonarQube - Tools'
        projectKey: '$(sonarQubeKey)'
        projectName: '$(sonarQubeName)'
        jdkversion: 'JAVA_HOME_17_X64'
      condition: not(contains(variables['Build.Repository.Name'], 'broad'))
      continueOnError: true
    - task: SonarQubePublish@7
      displayName: 'SI: Publish Quality Gate'
      inputs:
        SonarQube: 'SonarQube - Tools'
        projectKey: '$(sonarQubeKey)'
        projectName: '$(sonarQubeName)'
      continueOnError: true
      condition: not(contains(variables['Build.Repository.Name'], 'broad'))
    - task: CmdLine@2 
      displayName: 'SI: Sonarqube Tags'
      inputs:
        script: 'curl -u "${{ parameters.SonarAzureToken }}": -d "project=$(sonarQubeKey)&tags=$(sonarQubeTags)" -k -X POST "https://sonarqube.blip.tools/api/project_tags/set"'
      condition: not(contains(variables['Build.Repository.Name'], 'broad'))
      continueOnError: true
- ${{ else }}:
  - task: SonarQubeAnalyze@7
    displayName: 'SI: Run Code Analysis'
    inputs:
      SonarQube: 'SonarQube - Tools'
      projectKey: '$(sonarQubeKey)'
      projectName: '$(sonarQubeName)'
      jdkversion: 'JAVA_HOME_17_X64'
    condition: not(contains(variables['Build.Repository.Name'], 'broad'))
    continueOnError: true
  - task: SonarQubePublish@7
    displayName: 'SI: Publish Quality Gate'
    inputs:
      SonarQube: 'SonarQube - Tools'
      projectKey: '$(sonarQubeKey)'
      projectName: '$(sonarQubeName)'
    continueOnError: true
    condition: not(contains(variables['Build.Repository.Name'], 'broad'))
  - task: CmdLine@2 
    displayName: 'SI: Sonarqube Tags'
    inputs:
      script: 'curl -u "${{ parameters.SonarAzureToken }}": -d "project=$(sonarQubeKey)&tags=$(sonarQubeTags)" -k -X POST "https://sonarqube.blip.tools/api/project_tags/set"'
    condition: not(contains(variables['Build.Repository.Name'], 'broad'))
    continueOnError: true
# Example Pipeline with NuGet Publishing
# This demonstrates how to use the new NuGet publishing functionality

trigger:
  branches:
   include:
     - master
  paths:
    include:
      - "src/Applications/Scheduler"

resources:
  repositories:
    - repository: templates
      type: git
      name: Operações/template-take-blip
      ref: refs/tags/v1.4.75

variables:
  BuildCounter: $[counter('buildCounter',1)]
  sonarQubeTags: "Headway"
  sonarQubeKey: Blip_Scheduler_Application
  sonarQubeName: Blip Scheduler Application
  checkmarxName: Blip Scheduler Application
  sastFilter: "!**/**, src/Applications/Scheduler/Takenet.Iris.Application.Scheduler/**/*"
  scaFilter: "!**/**, src/Applications/Scheduler/Takenet.Iris.Application.Scheduler/**/*"
  framework: dotnet
  NUGET_PACKAGES: $(Pipeline.Workspace)/.nuget/packages

pool:
  name: 'Azure Pipelines'
  vmImage: windows-latest
  demands:
    - isIris -equals true

extends:
  template: "template-pipeline.yml@templates"
  parameters:
    # NEW: NuGet Publishing Configuration
    nugetAuthenticate: true
    vstsFeed: "BlipNuget"
    publishNugetPackages: true
    includeSymbols: true
    
    # Existing parameters
    packagename: "Iris-Scheduler"
    type: dotnet
    to: octopus
    dockerAgentPool: "ubuntu-latest"
    octopuspackagename: "Projects-1021"
    BuildCounter: $(BuildCounter)
    createZipPackage: true
    skipsonarbranch: false
    sonarInclusions: "src/Applications/Scheduler/Takenet.Iris.Application.Scheduler/**/*.cs"
    skipSast: true
    sastFilter: "!**/**, src/Applications/Scheduler/Takenet.Iris.Application.Scheduler/**/*"
    coverageTest: true
    timeoutInMinutes: 60
    createZipChart: true
    chartDir: "infra/helm/iris"
    contextpath: "."
    octopusArtifactName: "Takenet.Iris.Application.Scheduler"
    buildArtifactsDir: "Releases/Takenet.Iris.Application.Scheduler"
    dotnetPublish: true
    octopusPackageFormat: "NuPkg"
    restorePreSolution: false
    solution: "src/Applications/Scheduler/Takenet.Iris.Application.Scheduler.slnf"
    restoreSolution: true
    useCache: false
    dockerArguments: "--pull  --build-arg PROJECTPATH=Applications/Scheduler/Takenet.Iris.Application.Scheduler/Takenet.Iris.Application.Scheduler.csproj  --build-arg ENTRYPOINT=Takenet.Iris.Application.Scheduler.dll"
    dockerRepositoryName: "iris-scheduler-application"
    dockerTags: |
       latest
       scheduler-$(ProductBuildNumber)
    octopusReleaseChannel:
       - Msging
    csprojToPublish:
     - Takenet.Iris.Application.Scheduler.csproj
    publishTestResults: "**/*.trx"
    multiTests:
      - testname: Unit
        projects: "**/Takenet.Iris.Application.Scheduler.UnitTests.csproj"
        filters: '--filter FullyQualifiedName~Takenet.Iris.Application.Scheduler.UnitTests --configuration release --no-build --logger trx --collect:"XPlat Code Coverage" -- DataCollectionRunSettings.DataCollectors.DataCollector.Configuration.Include="[Takenet.Iris.*.Scheduler]*" DataCollectionRunSettings.DataCollectors.DataCollector.Configuration.Format=opencover'
      - testname: Acceptance
        projects: "**/Takenet.Iris.Application.Scheduler.AcceptanceTests.csproj"
        filters: '--filter TestCategory=scheduler --configuration release --no-build --logger trx  --collect:"XPlat Code Coverage" -- DataCollectionRunSettings.DataCollectors.DataCollector.Configuration.Include="[Takenet.Iris.*.Scheduler]*" DataCollectionRunSettings.DataCollectors.DataCollector.Configuration.Format=opencover'

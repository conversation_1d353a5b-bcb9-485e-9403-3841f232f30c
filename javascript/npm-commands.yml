parameters:
  - name: nodeVersion
    type: string
    default: '10.x' 
  - name: npmCmds
    type: object
    default: 
      - 'npm cache clean --force'
      - 'npm install'
      - 'npm run build'
  - name: skipCreateTag
    type: boolean 
    default: false
  - name: skipSonarBranch
    type: boolean 
  - name: sonarInclusions
    type: string
    default: '**/*'
  - name: sonarExclusions
    type: string
    default: ''
  - name: sonarTestInclusions
    type: string
    default: '**/*'
  - name: sonarExecutionReportPaths
    type: string
    default: ''
  - name: skipSast
    type: boolean
    default: true
  - name: sastFilter
    type: string
    default: '**/*'
  - name: scaFilter
    type: string
    default: '**/*'

steps:
  - ${{ if eq(parameters.skipSast, false) }}:
    - template: ../si/sast-analysis.yml
      parameters:
        sastFilter: ${{ parameters.sastFilter }}
        scaFilter: ${{ parameters.scaFilter }}
  - ${{ if eq(parameters.skipSonarBranch, false) }}:
    - template: ../si/pre-steps-template.yml
      parameters:
        sonarInclusions: ${{ parameters.sonarInclusions }}
        sonarExclusions: ${{ parameters.sonarExclusions }}
        sonarTestInclusions: ${{ parameters.sonarTestInclusions }}
        sonarExecutionReportPaths: ${{ parameters.sonarExecutionReportPaths }}
        type: javascript
  - task: UseNode@1
    inputs:
      version: '${{ parameters.nodeVersion }}'
  - ${{ each cmd in parameters.npmCmds }}:
    - task: PowerShell@2
      condition: succeeded()
      displayName: 'NPM cmd ${{ cmd }}'
      inputs:
        targetType: "inline"
        script: |
          Invoke-Expression "${{ cmd }}"
  - ${{ if eq(parameters.skipSonarBranch, false) }}:
    - template: ../si/pos-steps-template.yml
  - ${{ if eq(parameters.skipCreateTag, false) }}:
    - ${{ if contains(variables['build.sourceBranch'], 'hotfix') }}: 
      - template: ../tools/CreateTag.yml
        parameters:
          nameTag: $(ProductBuildNumber)
          isHotfix: true
          isBeta: false
          usePrefix: true
    - ${{ if contains(variables['build.sourceBranch'], 'beta') }}: 
      - template: ../tools/CreateTag.yml
        parameters:
          nameTag: $(ProductBuildNumber)
          isHotfix: false
          isBeta: true
          usePrefix: true
    - ${{ if or(eq(variables['build.sourceBranchName'], 'master'), eq(variables['build.sourceBranchName'], 'main')) }}:
      - template: ../tools/CreateTag.yml
        parameters:
          nameTag: $(ProductBuildNumber)
          isHotfix: false
          isBeta: false
          usePrefix: true
parameters:
  - name: nodeVersion
    type: string
    default: "10.x"
  - name: npmjscom
    type: string
    default: ""
  - name: npmCmds
    type: object
    default:
      - "npm cache clean --force"
      - "npm install"
      - "npm run build"
  - name: skipSonarBranch
    type: boolean

  - name: variableGroupName
    type: string
    default: "ReleaseNumberCount"
  - name: variableCountName
    type: string
    default: "applicationBuildCount"
  - name: useSharedBuildCount
    type: boolean
    default: false

jobs:
  - job: Publishing_Semantic_Release
    steps:
      - checkout: self
        persistCredentials: true
      - template: ../tools/ProductBuildNumber.yml
        parameters:
          variableGroupName: ${{ parameters.variableGroupName }}
          variableCountName: ${{ parameters.variableCountName }}
          useSharedBuildCount: ${{ parameters.useSharedBuildCount }}
      - task: PowerShell@2
        displayName: "Validando Variável {GH_TOKEN} "
        inputs:
          targetType: "inline"
          script: 'if ([string]::IsNullOrWhitespace($env:GH_TOKEN)) { throw "Não foi fornecida a variável {GH_TOKEN} para integração semantic-release" }'
      - task: PowerShell@2
        displayName: "Validando Variável {NPM_TOKEN} "
        inputs:
          targetType: "inline"
          script: 'if ([string]::IsNullOrWhitespace($env:NPM_TOKEN)) { throw "Não foi fornecida a variável {NPM_TOKEN} para integração semantic-release" }'
      - template: npm-commands.yml
        parameters:
          nodeVersion: ${{ parameters.nodeVersion }}
          npmCmds: ${{ parameters.npmCmds }}
          skipSonarBranch: ${{ parameters.skipSonarBranch }}
          skipCreateTag: true
      - task: PowerShell@2
        condition: succeeded()
        displayName: "NPM cmd npm run semantic-release"
        inputs:
          targetType: "inline"
          script: |
            Invoke-Expression "npm run semantic-release"

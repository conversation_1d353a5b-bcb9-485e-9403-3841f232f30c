parameters:
  - name: nodeVersion
    type: string
    default: '10.x'
  - name: npmCmds
    type: object
    default: 
      - 'npm cache clean --force'
      - 'npm install'
      - 'npm run build'
  - name: packagename
    type: string
    default: ''
  - name: createZipPackage
    type: boolean
    default: false
  - name: buildArtifactsDir
    type: string
    default: 'build'
  - name: skipSonarBranch
    type: boolean

  - name: sonarInclusions
    type: string
    default: '**/*'
  - name: sonarExclusions
    type: string
    default: ''
  - name: sonarTestInclusions
    type: string
    default: '**/*'

  - name: skipSast
    type: boolean
    default: true
  - name: sastFilter
    type: string
    default: '**/*'
  - name: scaFilter
    type: string
    default: '**/*'

  - name: variableGroupName
    type: string
    default: 'ReleaseNumberCount'
  - name: variableCountName
    type: string
    default: 'applicationBuildCount'
  - name: useSharedBuildCount
    type: boolean
    default: false   

jobs:
  - job: Publishing_Artifact
    steps:
    - checkout: self
      persistCredentials: true
    - template: ../tools/ProductBuildNumber.yml
      parameters:
        variableGroupName: ${{ parameters.variableGroupName }}
        variableCountName: ${{ parameters.variableCountName }}
        useSharedBuildCount: ${{ parameters.useSharedBuildCount }}
    - template: npm-commands.yml
      parameters:        
        nodeVersion: ${{ parameters.nodeVersion }}
        npmCmds: ${{ parameters.npmCmds }}
        skipSonarBranch: ${{ parameters.skipSonarBranch }}
        skipSast: ${{ parameters.skipSast }}
        sastFilter: ${{ parameters.sastFilter }}
        scaFilter: ${{ parameters.scaFilter }}
        sonarInclusions: ${{ parameters.sonarInclusions }}
        sonarExclusions: ${{ parameters.sonarExclusions }}
        sonarTestInclusions: ${{ parameters.sonarTestInclusions }}
    - ${{ if eq(parameters.createZipPackage, true) }}:
      - task: ArchiveFiles@2
        displayName: 'Archive ${{ parameters.buildArtifactsDir }} folder'
        inputs:
            rootFolderOrFile: ${{ parameters.buildArtifactsDir }}
            archiveFile: '$(Build.ArtifactStagingDirectory)/${{ parameters.packagename }}-$(ProductBuildNumber).zip'
            includeRootFolder: false
      - task: PublishBuildArtifacts@1
        inputs:
          PathtoPublish: '$(Build.ArtifactStagingDirectory)/${{ parameters.packagename }}-$(ProductBuildNumber).zip'
          publishLocation: 'Container'
    - ${{ if eq(parameters.createZipPackage, false) }}:
      - task: PublishBuildArtifacts@1
        inputs:
          PathtoPublish: '$(Build.SourcesDirectory)/dist'
          ArtifactName: '${{ parameters.packagename }}-$(ProductBuildNumber)'
          publishLocation: 'Container'
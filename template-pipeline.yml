parameters:
  # defaults parameters

  - name: packagename
    type: string
    displayName: "Name of the Project"
  - name: type
    default: dotnet
    type: string
    displayName: "The type of your stack"
  - name: from
    default: csps
    displayName: "Project configuration"
  - name: to
    displayName: What's the target of your artefact
    default: "kubernetes"
  - name: deploy
    displayName: What's the target of your deploy
    default: ""
  - name: stages
    type: object
    default: []
    displayName: "Stages of your project"
  - name: configPath
    type: string
    displayName: "appsettings.json path"
    default: ""
  - name: akvReplaceEscapeType
    type: string
    default: auto
    values:
      - auto
      - none
      - json
      - xml
  - name: skipSonarBranch
    type: boolean
    default: false
  - name: sonarInclusions
    type: string
    default: "**/*"
  - name: sonarExclusions
    type: string
    default: ""
  - name: sonarTestInclusions
    type: string
    default: "**/*"
  - name: sonarExecutionReportPaths
    type: string
    default: ""
  - name: skipSast
    type: boolean
    default: true
  - name: sastFilter
    type: string
    default: "**/*"
  - name: scaFilter
    type: string
    default: "**/*"
  - name: applyKubernetesCertificate
    type: boolean
    default: false
  - name: globalHelm
    type: boolean
    default: false
  - name: chartVersion
    type: string
    default: ""
  - name: namespace
    type: string
    default: "default"
  - name: gitSubmodules
    type: object
    default:
      enabled: false
      customVersions: []

  # octopus parameters

  - name: octopuspackagename
    type: string
    default: ""
    displayName: "Deploy Octopus project name"
  - name: BuildCounter
    displayName: "Counter of build"
    default: ""
  - name: buildArtifactsDir
    type: string
    default: "dist"
  - name: octopusSpace
    type: string
    default: "Spaces-1"
  - name: createZipPackage
    type: boolean
    default: true
  - name: octopusArtifactName
    type: string
    displayName: "Octopus package name"
    default: ""
  - name: octopusPackageFormat
    type: string
    default: "Zip"
  - name: dockerBuildPush
    type: boolean
    default: true
  - name: octopusReleaseChannel
    type: object
    default:
      - "Default"
  - name: createZipChart
    type: boolean
    default: false
  - name: chartDir
    type: string
    default: ""
  - name: dockerRepositoryName
    type: string
    default: ""
  - name: dockerTags
    type: string
    default: "$(ProductBuildNumber)"
  - name: dockerAgentPool
    type: string
    default: ""
  - name: variableGroupName
    type: string
    default: "ReleaseNumberCount"
  - name: variableCountName
    type: string
    default: "applicationBuildCount"
  - name: useSharedBuildCount
    type: boolean
    default: false
  - name: hasFrontEndDeploy
    type: boolean
    default: false
  - name: frontEndHelmPackageVersion
    type: string
    default: "blip-frontend:0.3.12"

  # Javascript/npm parameters
  - name: nodeVersion
    displayName: "Node Version"
    default: "10.x"
  - name: hasBundle
    type: boolean
    default: false
  - name: npmCmds
    type: object
    default:
      - "npm cache clean --force"
      - "npm install"
      - "npm run build"

  # Python parameters
  - name: pythonVersion
    type: string
    default: "3.x"
  - name: pythonCmds
    type: object
    default: ""

  # Ruby parameters
  - name: rubyVersion
    type: string
    default: "2.4.x"
  - name: rubyCmds
    type: object
    default: ""

  # Terraform parameters
  - name: outPutPathPlan
    type: "string"
    displayName: "Diretório que o plan to terraform será aplicado"
    default: "./outputplan"
  - name: workingDirectory
    type: "string"
    displayName: "Diretório base dos arquivos terraform"
    default: "./"
  - name: azRmResourceGroup
    type: "string"
    displayName: "Resource Group do ARM"
    default: ""
  - name: azRmStorageAccount
    type: "string"
    displayName: "Storage Account do ARM"
    default: ""
  - name: azRmContainer
    type: "string"
    displayName: "Nome do Container no Storage Account"
    default: ""
  - name: azRmKey
    type: "string"
    displayName: "Key de Acesso do Storage Account"
    default: ""
  - name: backendServiceArm
    type: "string"
    displayName: "Subscription Backend - Service Connection"
    default: ""
  - name: terraformCommand
    type: "string"
    displayName: "Terraform command (apply or destroy)"
    default: "apply"
  - name: planArguments
    type: "string"
    displayName: "Arguments for Plan command"
    default: ""

  # Nuget Parameters
  - name: nugetServiceConnection
    type: string
    displayName: "Name of the service connection of nuget service"
    default: ""
  - name: includeSymbols
    type: boolean
    default: false

  # Docker parameters

  - name: dockerfiledir
    type: string
    default: "**/Dockerfile"
    displayName: "Dockerfile Dir, default is **/Dockerfile"
  - name: contextpath
    type: string
    default: "."
    displayName: "Context of Docker, default is ."
  - name: dockerArguments
    type: string
    default: ""

  # .Net Parameters

  - name: chartpath
    default: ""
  - name: buildConfiguration
    type: string
    default: release
  - name: coverageTest
    type: boolean
    default: false
  - name: testParameters
    type: string
    default: "/p:CollectCoverage=true /p:CoverletOutputFormat=opencover /p:CoverletOutput=$(Build.SourcesDirectory)/coverage/"
  - name: publishTestResults
    type: string
    default: "none"
  - name: testProjects
    type: string
    default: "**/*Test*.csproj"
  - name: dotnetVersion
    type: string
    default: ""
  - name: dotnetPublish
    type: boolean
    default: false
  - name: testPermission
    type: boolean
    default: false
  - name: skipTestToNuget
    type: boolean
    default: false
  - name: vstsFeed
    type: string
    default: ""
  - name: preSolution
    type: string
    default: ""
  - name: solution
    type: string
    default: "**/*.sln"
  - name: multiTests
    type: object
    default:
      - "none"
  - name: csprojToPublish
    type: object
    default:
      - "none"
  - name: timeoutInMinutes
    type: string
    default: "60"
  - name: csprojToPack
    type: object
    default:
      - "none"
  - name: restorePreSolution
    type: boolean
    default: true
  - name: restoreSolution
    type: boolean
    default: true
  - name: useCache
    type: boolean
    default: true
  - name: ServiceConnectionDocker
    type: string
    default: ""
  - name: nugetAuthenticate
    type: boolean
    default: false
  - name: publishNugetPackages
    type: boolean
    default: false
    displayName: "Enable NuGet package publishing to vstsFeed"

  # NPMJS Parameters
  - name: npmjscom
    type: string
    default: ""

  #PYPI Parameters
  - name: pypicom
    type: string
    default: ""

  # Webapp Parameters
  - name: webAppName
    type: string
    default: ""
  - name: webAppAppSettings
    type: string
    default: ""
  - name: webAppAppType
    type: string
    default: "webApp"
  - name: webAppPackage
    type: string
    default: "$(Pipeline.Workspace)/**/*.zip"
  - name: webAppTakeAppOffline
    type: boolean
    default: true
  - name: webAppUseWebDeploy
    type: boolean
    default: true
  - name: webAppRemoveAdditionalFiles
    type: boolean
    default: true
  - name: webAppRenameFiles
    type: boolean
    default: true
  - name: webAppJSONFiles
    type: string
    default: "*/settings.json"
  - name: formatAppSettingsBeforeDelivery
    type: boolean
    default: false
  - name: configurationFromEnvFile
    type: boolean
    default: false


stages:
  - stage: build
    displayName: "Build your project"
    jobs:
      - ${{ if and(eq(parameters.type, 'dotnet'),eq(parameters.to, 'kubernetes')) }}:
          - template: kubernetes/template-ci-helm.yml
            parameters:
              packagename: ${{ parameters.packagename }}
              chartpath: ${{ parameters.chartpath }}
              configPath: ${{ parameters.configPath }}
              buildConfiguration: ${{ parameters.buildConfiguration }}
              dockerfiledir: ${{ parameters.dockerfiledir }}
              contextpath: ${{ parameters.contextpath }}
              from: ${{ parameters.from }}
              dotnetVersion: ${{ parameters.dotnetVersion }}
              coverageTest: ${{ parameters.coverageTest }}
              testParameters: ${{ parameters.testParameters }}
              testProjects: ${{ parameters.testProjects }}
              publishTestResults: ${{ parameters.publishTestResults }}
              type: ${{ parameters.type }}
              vstsFeed: ${{ parameters.vstsFeed }}
              skipSonarBranch: ${{ parameters.skipSonarBranch }}
              sonarInclusions: ${{ parameters.sonarInclusions }}
              sonarExclusions: ${{ parameters.sonarExclusions }}
              sonarTestInclusions: ${{ parameters.sonarTestInclusions }}
              skipSast: ${{ parameters.skipSast }}
              sastFilter: ${{ parameters.sastFilter }}
              scaFilter: ${{ parameters.scaFilter }}
              dockerArguments: ${{ parameters.dockerArguments }}
              ServiceConnectionDocker: ${{ parameters.ServiceConnectionDocker }}
              nugetAuthenticate: ${{ parameters.nugetAuthenticate }}
              globalHelm: ${{ parameters.globalHelm }}
              configurationFromEnvFile: ${{ parameters.configurationFromEnvFile }}
              useCache: ${{ parameters.useCache }}
      - ${{ if and(eq(parameters.type, 'dotnet'),eq(parameters.to, 'basic')) }}:
          - template: dotnet/template-ci-none.yml
            parameters:
              packagename: ${{ parameters.packagename }}
              from: ${{ parameters.from }}
              coverageTest: ${{ parameters.coverageTest }}
              testParameters: ${{ parameters.testParameters }}
              testProjects: ${{ parameters.testProjects }}
              vstsFeed: ${{ parameters.vstsFeed }}
              skipSonarBranch: ${{ parameters.skipSonarBranch }}
      - ${{ if and(eq(parameters.type, 'dotnet'),eq(parameters.to, 'nuget')) }}:
          - template: dotnet/template-ci-nuget.yml
            parameters:
              packagename: ${{ parameters.packagename }}
              from: ${{ parameters.from }}
              coverageTest: ${{ parameters.coverageTest }}
              includeSymbols: ${{ parameters.includeSymbols }}
              testParameters: ${{ parameters.testParameters }}
              testPermission: ${{ parameters.testPermission }}
              skipTestToNuget: ${{ parameters.skipTestToNuget }}
              testProjects: ${{ parameters.testProjects }}
              dotnetVersion: ${{ parameters.dotnetVersion }}
              vstsFeed: ${{ parameters.vstsFeed }}
              skipSonarBranch: ${{ parameters.skipSonarBranch }}
              skipSast: ${{ parameters.skipSast }}
              sastFilter: ${{ parameters.sastFilter }}
              scaFilter: ${{ parameters.scaFilter }}
              csprojToPack: ${{ parameters.csprojToPack }}
              solution: ${{ parameters.solution }}
      - ${{ if and(eq(parameters.type, 'dotnet'),eq(parameters.to, 'octopus')) }}:
          - template: octopus/template-ci-steps.yml
            parameters:
              packagename: ${{ parameters.packagename }}
              octopuspackagename: ${{ parameters.octopuspackagename }}
              BuildCounter: ${{ parameters.BuildCounter }}
              buildArtifactsDir: ${{ parameters.buildArtifactsDir }}
              dockerfiledir: ${{ parameters.dockerfiledir }}
              contextpath: ${{ parameters.contextpath }}
              octopusSpace: ${{ parameters.octopusSpace }}
              createZipPackage: ${{ parameters.createZipPackage }}
              octopusArtifactName: ${{ parameters.octopusArtifactName }}
              coverageTest: ${{ parameters.coverageTest }}
              type: ${{ parameters.type }}
              configPath: ${{ parameters.configPath }}
              buildConfiguration: ${{ parameters.buildConfiguration }}
              testParameters: ${{ parameters.testParameters }}
              publishTestResults: ${{ parameters.publishTestResults }}
              testProjects: ${{ parameters.testProjects }}
              dotnetVersion: ${{ parameters.dotnetVersion }}
              dotnetPublish: ${{ parameters.dotnetPublish }}
              octopusPackageFormat: ${{ parameters.octopusPackageFormat }}
              vstsFeed: ${{ parameters.vstsFeed }}
              skipSonarBranch: ${{ parameters.skipSonarBranch }}
              sonarInclusions: ${{ parameters.sonarInclusions }}
              sonarExclusions: ${{ parameters.sonarExclusions }}
              sonarTestInclusions: ${{ parameters.sonarTestInclusions }}
              skipSast: ${{ parameters.skipSast }}
              sastFilter: ${{ parameters.sastFilter }}
              scaFilter: ${{ parameters.scaFilter }}
              preSolution: ${{ parameters.preSolution }}
              solution: ${{ parameters.solution }}
              multiTests: ${{ parameters.multiTests }}
              csprojToPublish: ${{ parameters.csprojToPublish }}
              dockerBuildPush: ${{ parameters.dockerBuildPush }}
              octopusReleaseChannel: ${{ parameters.octopusReleaseChannel }}
              dockerArguments: ${{ parameters.dockerArguments }}
              createZipChart: ${{ parameters.createZipChart }}
              chartDir: ${{ parameters.chartDir }}
              dockerRepositoryName: ${{ parameters.dockerRepositoryName }}
              dockerTags: ${{ parameters.dockerTags }}
              timeoutInMinutes: ${{ parameters.timeoutInMinutes }}
              dockerAgentPool: ${{ parameters.dockerAgentPool }}
              restorePreSolution: ${{ parameters.restorePreSolution }}
              restoreSolution: ${{ parameters.restoreSolution }}
              useCache: ${{ parameters.useCache }}
              variableGroupName: ${{ parameters.variableGroupName }}
              variableCountName: ${{ parameters.variableCountName }}
              useSharedBuildCount: ${{ parameters.useSharedBuildCount }}
              hasFrontEndDeploy: ${{ parameters.hasFrontEndDeploy }}
              frontEndHelmPackageVersion: ${{ parameters.frontEndHelmPackageVersion }}
              gitSubmodules: ${{ parameters.gitSubmodules }}
              nugetAuthenticate: ${{ parameters.nugetAuthenticate }}
              publishNugetPackages: ${{ parameters.publishNugetPackages }}

      - ${{ if and(eq(parameters.type, 'javascript'),eq(parameters.to, 'kubernetes')) }}:
          - template: kubernetes/template-ci-helm.yml
            parameters:
              packagename: ${{ parameters.packagename }}
              chartpath: ${{ parameters.chartpath }}
              configPath: ${{ parameters.configPath }}
              dockerfiledir: ${{ parameters.dockerfiledir }}
              contextpath: ${{ parameters.contextpath }}
              from: ${{ parameters.from }}
              type: ${{ parameters.type }}
              nodeVersion: ${{ parameters.nodeVersion }}
              npmCmds: ${{ parameters.npmCmds }}
              skipSonarBranch: ${{ parameters.skipSonarBranch }}
              sonarInclusions: ${{ parameters.sonarInclusions }}
              sonarExclusions: ${{ parameters.sonarExclusions }}
              sonarTestInclusions: ${{ parameters.sonarTestInclusions }}
              skipSast: ${{ parameters.skipSast }}
              sastFilter: ${{ parameters.sastFilter }}
              scaFilter: ${{ parameters.scaFilter }}
              dockerArguments: ${{ parameters.dockerArguments }}
              ServiceConnectionDocker: ${{ parameters.ServiceConnectionDocker }}
              akvReplaceEscapeType: ${{ parameters.akvReplaceEscapeType }}
              stages: ${{ parameters.stages }}
              formatAppSettingsBeforeDelivery: ${{parameters.formatAppSettingsBeforeDelivery}}
              globalHelm: ${{ parameters.globalHelm }}
              configurationFromEnvFile: ${{ parameters.configurationFromEnvFile }}
      - ${{ if and(eq(parameters.type, 'javascript'),eq(parameters.to, 'octopus')) }}:
          - template: octopus/template-ci-steps.yml
            parameters:
              packagename: ${{ parameters.packagename }}
              octopuspackagename: ${{ parameters.octopuspackagename }}
              BuildCounter: ${{ parameters.BuildCounter }}
              configPath: ${{ parameters.configPath }}
              octopusArtifactName: ${{ parameters.octopusArtifactName }}
              buildArtifactsDir: ${{ parameters.buildArtifactsDir }}
              createZipPackage: ${{ parameters.createZipPackage }}
              nodeVersion: ${{ parameters.nodeVersion }}
              npmCmds: ${{ parameters.npmCmds }}
              contextpath: ${{ parameters.contextpath }}
              type: ${{ parameters.type }}
              skipSonarBranch: ${{ parameters.skipSonarBranch }}
              skipSast: ${{ parameters.skipSast }}
              sastFilter: ${{ parameters.sastFilter }}
              scaFilter: ${{ parameters.scaFilter }}
              dockerBuildPush: ${{ parameters.dockerBuildPush }}
              octopusReleaseChannel: ${{ parameters.octopusReleaseChannel }}
              dockerAgentPool: ${{ parameters.dockerAgentPool }}
              variableGroupName: ${{ parameters.variableGroupName }}
              variableCountName: ${{ parameters.variableCountName }}
              useSharedBuildCount: ${{ parameters.useSharedBuildCount }}
              hasFrontEndDeploy: ${{ parameters.hasFrontEndDeploy }}
              frontEndHelmPackageVersion: ${{ parameters.frontEndHelmPackageVersion }}
              sonarExecutionReportPaths: ${{ parameters.sonarExecutionReportPaths }}
              gitSubmodules: ${{ parameters.gitSubmodules }}
      - ${{ if and(eq(parameters.type, 'ruby'),eq(parameters.to, 'octopus')) }}:
          - template: octopus/template-ci-steps.yml
            parameters:
              rubyVersion: ${{ parameters.rubyVersion }}
              rubyCmds: ${{ parameters.rubyCmds }}
              skipSonarBranch: ${{ parameters.skipSonarBranch }}
              packagename: ${{ parameters.packagename }}
              octopuspackagename: ${{ parameters.octopuspackagename }}
              BuildCounter: ${{ parameters.BuildCounter }}
              configPath: ${{ parameters.configPath }}
              octopusArtifactName: ${{ parameters.octopusArtifactName }}
              buildArtifactsDir: ${{ parameters.buildArtifactsDir }}
              createZipPackage: ${{ parameters.createZipPackage }}
              contextpath: ${{ parameters.contextpath }}
              type: ${{ parameters.type }}
              dockerBuildPush: ${{ parameters.dockerBuildPush }}
              octopusReleaseChannel: ${{ parameters.octopusReleaseChannel }}
              dockerAgentPool: ${{ parameters.dockerAgentPool }}
              variableGroupName: ${{ parameters.variableGroupName }}
              variableCountName: ${{ parameters.variableCountName }}
              useSharedBuildCount: ${{ parameters.useSharedBuildCount }}
              hasFrontEndDeploy: ${{ parameters.hasFrontEndDeploy }}
              frontEndHelmPackageVersion: ${{ parameters.frontEndHelmPackageVersion }}
              gitSubmodules: ${{ parameters.gitSubmodules }}
      - ${{ if and(eq(parameters.type, 'javascript'),eq(parameters.to, 'semantic-release')) }}:
          - template: javascript/npm-semantic-ci.yml
            parameters:
              nodeVersion: ${{ parameters.nodeVersion }}
              npmCmds: ${{ parameters.npmCmds }}
              npmjscom: ${{ parameters.npmjscom }}
              skipSonarBranch: ${{ parameters.skipSonarBranch }}
      - ${{ if and(eq(parameters.type, 'javascript'),eq(parameters.to, 'artifact')) }}:
          - template: javascript/npm-artifact-ci.yml
            parameters:
              nodeVersion: ${{ parameters.nodeVersion }}
              npmCmds: ${{ parameters.npmCmds }}
              packagename: ${{ parameters.packagename }}
              createZipPackage: ${{ parameters.createZipPackage }}
              buildArtifactsDir: ${{ parameters.buildArtifactsDir }}
              skipSonarBranch: ${{ parameters.skipSonarBranch }}
              sonarInclusions: ${{ parameters.sonarInclusions }}
              sonarExclusions: ${{ parameters.sonarExclusions }}
              sonarTestInclusions: ${{ parameters.sonarTestInclusions }}
              skipSast: ${{ parameters.skipSast }}
              sastFilter: ${{ parameters.sastFilter }}
              scaFilter: ${{ parameters.scaFilter }}
              variableGroupName: ${{ parameters.variableGroupName }}
              variableCountName: ${{ parameters.variableCountName }}
              useSharedBuildCount: ${{ parameters.useSharedBuildCount }}
      - ${{ if and(eq(parameters.type, 'python'),eq(parameters.to, 'pypi')) }}:
          - template: python/template-ci.yml
            parameters:
              pythonVersion: ${{ parameters.pythonVersion }}
              pythonCmds: ${{ parameters.pythonCmds }}
              pypicom: ${{ parameters.pypicom }}
              skipSonarBranch: ${{ parameters.skipSonarBranch }}
      - ${{ if eq(parameters.type, 'terraform') }}:
          - template: terraform/template-ci.yml
            parameters:
              workingDirectory: ${{ parameters.workingDirectory }}
              azRmResourceGroup: ${{ parameters.azRmResourceGroup }}
              azRmStorageAccount: ${{ parameters.azRmStorageAccount }}
              azRmContainer: ${{ parameters.azRmContainer }}
              azRmKey: ${{ parameters.azRmKey }}
              backendServiceArm: ${{ parameters.backendServiceArm }}
              planArguments: ${{ parameters.planArguments }}
              fileVariables: ${{ parameters.configPath }}
              skipSonarBranch: ${{ parameters.skipSonarBranch }}
  - ${{ if and(and(eq(parameters.type, 'dotnet'),eq(parameters.to, 'nuget')),ne(variables['Build.Reason'], 'PullRequest'))  }}:
      - stage: "cd_nuget_package"
        displayName: "Publish Nuget Package"
        dependsOn: build
        jobs:
          - template: dotnet/template-cd-nuget.yml
            parameters:
              packagename: ${{parameters.packagename}}
              nugetServiceConnection: ${{parameters.nugetServiceConnection}}
  - ${{ each stage in parameters.stages  }}:
      - ${{ if ne(parameters.type, 'terraform') }}:
          - stage: ${{ stage.name }}
            displayName: "Deploy ${{ stage.name }} Enviroment"
            dependsOn: ${{ stage.dependsOn }}
            condition: ${{ stage.condition }}
            jobs:
              - ${{ if eq(parameters.deploy, 'webapp') }}:
                  - template: webapp/template-cd.yml
                    parameters:
                      variables: ${{ stage.variables }}
                      environment: ${{ stage.environment }}
                      serviceConnection: ${{ stage.ServiceConnection }}
                      webAppName: ${{ stage.webAppName }}
                      ${{ if ne(stage.webAppAppSettings, '') }}:
                        webAppAppSettings: ${{ stage.webAppAppSettings }}
                      ${{ if ne(stage.webAppAppType, '') }}:
                        webAppAppType: ${{ stage.webAppAppType }}
                      ${{ if ne(stage.webAppPackage, '') }}:
                        webAppPackage: ${{ stage.webAppPackage }}
                      ${{ if ne(stage.webAppTakeAppOffline, '') }}:
                        webAppTakeAppOffline: ${{ stage.webAppTakeAppOffline }}
                      ${{ if ne(stage.webAppUseWebDeploy, '') }}:
                        webAppUseWebDeploy: ${{ stage.webAppUseWebDeploy }}
                      ${{ if ne(stage.webAppRemoveAdditionalFiles, '') }}:
                        webAppRemoveAdditionalFiles: ${{ stage.webAppRemoveAdditionalFiles }}
                      ${{ if ne(stage.webAppRenameFiles, '') }}:
                        webAppRenameFiles: ${{ stage.webAppRenameFiles }}
                      ${{ if ne(stage.webAppJSONFiles, '') }}:
                        webAppJSONFiles: ${{ stage.webAppJSONFiles }}
              - ${{ else }}:
                  - template: kubernetes/template-cd.yml
                    parameters:
                      formatAppSettingsBeforeDelivery: ${{parameters.formatAppSettingsBeforeDelivery}}
                      variables: ${{ stage.variables }}
                      environment: ${{ stage.environment }}
                      packagename: ${{parameters.packagename}}
                      serviceConnection: ${{ stage.ServiceConnection }}
                      from: ${{ parameters.from }}
                      helmCustomParameters: ${{ stage.helmCustomParameters }}
                      akvReplaceEscapeType: ${{ parameters.akvReplaceEscapeType }}
                      applyKubernetesCertificate: ${{ parameters.applyKubernetesCertificate }}
                      globalHelm: ${{ parameters.globalHelm }}
                      chartVersion: ${{ parameters.chartVersion }}
                      namespace: ${{ parameters.namespace }}
                      configurationFromEnvFile: ${{ parameters.configurationFromEnvFile }}
                      ${{ if ne(stage.shouldAddAlarm, '') }}:
                        shouldAddAlarm: ${{ stage.shouldAddAlarm }}
                      ${{ if ne(stage.shouldAddApiStatusAlarm, '') }}:
                        shouldAddApiStatusAlarm: ${{ stage.shouldAddApiStatusAlarm }}
                      ${{ if ne(stage.shouldAddApiErrorsAlarm, '') }}:
                        shouldAddApiErrorsAlarm: ${{ stage.shouldAddApiErrorsAlarm }}
                      ${{ if ne(stage.shouldAddBotStatusAlarm, '') }}:
                        shouldAddBotStatusAlarm: ${{ stage.shouldAddBotStatusAlarm }}
                      ${{ if ne(stage.shouldAddBotErrorsAlarm, '') }}:
                        shouldAddBotErrorsAlarm: ${{ stage.shouldAddBotErrorsAlarm }}

      - ${{ if and(eq(parameters.type, 'terraform'), eq(parameters.terraformCommand, 'apply')) }}:
          - stage: ${{ stage.name }}
            displayName: "Deploy ${{ stage.name }} Enviroment"
            dependsOn: ${{ stage.dependsOn }}
            condition: ${{ stage.condition }}
            jobs:
              - template: terraform/template-cd-apply.yml
                parameters:
                  workingDirectory: ${{ parameters.workingDirectory }}
                  variables: ${{ stage.variables }}
                  environment: ${{ stage.environment }}
                  packagename: ${{parameters.packagename}}
                  azRmResourceGroup: ${{ parameters.azRmResourceGroup }}
                  azRmStorageAccount: ${{ parameters.azRmStorageAccount }}
                  azRmContainer: ${{ parameters.azRmContainer }}
                  azRmKey: ${{ parameters.azRmKey }}
                  backendServiceArm: ${{ parameters.backendServiceArm }}
                  environmentServiceNameAzureRM: ${{ stage.environmentServiceNameAzureRM }}
                  fileVariables: ${{ parameters.configPath }}
      - ${{ if and(eq(parameters.type, 'terraform'), eq(parameters.terraformCommand, 'destroy')) }}:
          - stage: ${{ stage.name }}
            displayName: "Deploy ${{ stage.name }} Enviroment"
            dependsOn: ${{ stage.dependsOn }}
            condition: ${{ stage.condition }}
            jobs:
              - template: terraform/template-cd-destroy.yml
                parameters:
                  environment: ${{ stage.environment }}
                  packagename: ${{stage.packagename}}
                  azRmResourceGroup: ${{ parameters.azRmResourceGroup }}
                  azRmStorageAccount: ${{ parameters.azRmStorageAccount }}
                  azRmContainer: ${{ parameters.azRmContainer }}
                  azRmKey: ${{ parameters.azRmKey }}
                  backendServiceArm: ${{ parameters.backendServiceArm }}
                  environmentServiceNameAzureRM: ${{ stage.environmentServiceNameAzureRM }}

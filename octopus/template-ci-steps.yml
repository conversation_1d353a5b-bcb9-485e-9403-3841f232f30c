parameters:
  - name: packagename
    type: string
    displayName: 'Nome do Projeto'
  - name: dockerfiledir
    type: string
    default: "**/Dockerfile"
    displayName: 'Dockerfile Dir, default is **/Dockerfile'
  - name: contextpath
    type: string
    default: "."
    displayName: 'Context of Docker, default is .'
  - name: octopuspackagename
    type: string
    displayName: 'Deploy Octopus project name'
  - name: BuildCounter
    displayName: 'Counter of build'
  - name: octopusSpace
    type: string
    default: "Spaces-1"
  - name: configPath
    type: string
  - name: createZipPackage
    type: boolean
    default: true
  - name: nodeVersion
    type: string
    default: ''
  - name: octopusArtifactName
    type: string
    displayName: 'Octopus package name'
  - name: buildArtifactsDir
    type: string
    default: 'dist'
  - name: coverageTest
    type: boolean
    default: false
  - name: type
    default: dotnet
    type: string
    displayName: "The type of your stack"
  - name: buildConfiguration
    type: string
    default: release
  - name: testParameters
    type: string
    default: '/p:CollectCoverage=true /p:CoverletOutputFormat=opencover /p:CoverletOutput=$(Build.SourcesDirectory)/coverage/'
  - name: publishTestResults
    type: string
    default: 'none'
  - name: testProjects
    type: string
    default: '**/*Test*.csproj'
  - name: npmCmds
    type: object
    default:
      - 'npm cache clean --force'
      - 'npm install'
      - 'npm run build'
  - name: dotnetVersion
    type: string
    default: ''
  - name: dotnetPublish
    type: boolean
    default: false
  - name: octopusPackageFormat
    type: string
    default: 'Zip'
  - name: vstsFeed
    type: string
    default: ''
  - name: skipSonarBranch
    type: boolean
  - name: sonarInclusions
    type: string
    default: '**/*'
  - name: sonarExclusions
    type: string
    default: ''
  - name: sonarTestInclusions
    type: string
    default: '**/*'
  - name: sonarExecutionReportPaths
    type: string
    default: ''
  - name: skipSast
    type: boolean
    default: true
  - name: sastFilter
    type: string
    default: '**/*'
  - name: scaFilter
    type: string
    default: '**/*'
  - name: preSolution
    type: string
    default: ''
  - name: solution
    type: string
    default: '**/*.sln'
  - name: multiTests
    type: object
    default:
      - 'none'
  - name: csprojToPublish
    type: object
    default:
      - 'none'
  - name: dockerBuildPush
    type: boolean
    default: true
  - name: octopusReleaseChannel
    type: object
    default:
      - 'Default'
  - name: dockerArguments
    type: string
    default: ''
  - name: createZipChart
    type: boolean
    default: false
  - name: chartDir
    type: string
    default: ''
  - name: dockerRepositoryName
    type: string
    default: ''
  - name: dockerTags
    type: string
    default: '$(ProductBuildNumber)'
  - name: timeoutInMinutes
    type: string
    default: '60'
  - name: rubyVersion
    type: string
    default: '2.4.x'
  - name: rubyCmds
    type: object
    default: ''
  - name: dockerAgentPool
    type: string
    default: ''
  - name: restorePreSolution
    type: boolean
    default: true
  - name: restoreSolution
    type: boolean
    default: true
  - name: useCache
    type: boolean
    default: true
  - name: gitSubmodules
    type: object
    default:
      enabled: false
      customVersions: []

  - name: variableGroupName
    type: string
    default: 'ReleaseNumberCount'
  - name: variableCountName
    type: string
    default: 'applicationBuildCount'
  - name: useSharedBuildCount
    type: boolean
    default: false

  - name: hasFrontEndDeploy
    type: boolean
    default: false
  - name: frontEndHelmPackageVersion
    type: string
    default: 'blip-frontend:0.3.12'

jobs:
  - job: Octopus_Build_Push
    variables:
    - group: ${{ parameters.variableGroupName }}
    timeoutInMinutes: ${{ parameters.timeoutInMinutes }}

    steps:
      - checkout: self
        persistCredentials: true
      - script: |
          printf "[INFO] Checking out submodules with received parameter \ngitSubmodules: \n${{ convertToJson(parameters.gitSubmodules) }}\n\n"
          gitSubmodulesCustomVersions=$(echo ${gitSubmodulesCustomVersions#\"}) # Remove leading double quote
          gitSubmodulesCustomVersions=$(echo ${gitSubmodulesCustomVersions%\"}) # Remove trailing double quote
          for submodule in $(echo ${gitSubmodulesCustomVersions} | jq -c ".[]"); do
            printf "[INFO] Cloning submodule repository:\n $submodule\n---\n"
            repo="$(echo $submodule | jq -r '.repo')"
            path="./$(echo $submodule | jq -r '.path')"
            ref="$(echo $submodule | jq -r '.ref')"

            if [ -d "$path" ]; then
              cd "$path"
              git init
              git remote add origin $repo
            else
              mkdir -p "$path"
              cd "$path"
              git -c http.extraheader="AUTHORIZATION: bearer $(System.AccessToken)" clone $repo .
            fi

            git -c http.extraheader="AUTHORIZATION: bearer $(System.AccessToken)" fetch --all
            git checkout $ref
            cd -
          done
        displayName: 'Git submodules init'
        condition: and(succeeded(), eq(variables['Agent.OS'], 'Linux'), eq(${{ parameters.gitSubmodules.enabled }}, true))
        env:
          gitSubmodulesCustomVersions: ${{ convertToJSON(parameters.gitSubmodules.customVersions) }}
      - script: |
          echo "checking date"
          date
          echo "setting date to America/Sao_Paulo"
          sudo timedatectl set-timezone "America/Sao_Paulo"
          date
        displayName: 'Set TimeZone Linux'
        condition: and(succeeded(), eq(variables['Agent.OS'], 'Linux'))
      - script: |
          tzutil /g
          tzutil /s "E. South America Standard Time"
          tzutil /g
        displayName: 'Set TimeZone Windows'
        condition: and(succeeded(), eq(variables['Agent.OS'], 'Windows_NT'))
      - ${{ if eq(parameters.type, 'dotnet') }}:
        - template: ../dotnet/dotnet-commands.yml
          parameters:
            buildConfiguration: ${{parameters.buildConfiguration}}
            coverageTest: ${{parameters.coverageTest}}
            testParameters: ${{ parameters.testParameters }}
            publishTestResults: ${{ parameters.publishTestResults }}
            testProjects: ${{ parameters.testProjects }}
            dotnetVersion: ${{ parameters.dotnetVersion }}
            dotnetPublish: ${{ parameters.dotnetPublish }}
            vstsFeed: ${{ parameters.vstsFeed }}
            skipSonarBranch: ${{ parameters.skipSonarBranch }}
            sonarInclusions: ${{ parameters.sonarInclusions }}
            sonarExclusions: ${{ parameters.sonarExclusions }}
            sonarTestInclusions: ${{ parameters.sonarTestInclusions }}
            skipSast: ${{ parameters.skipSast }}
            sastFilter: ${{ parameters.sastFilter }}
            scaFilter: ${{ parameters.scaFilter }}
            preSolution: ${{ parameters.preSolution }}
            solution: ${{ parameters.solution }}
            multiTests: ${{ parameters.multiTests }}
            csprojToPublish: ${{ parameters.csprojToPublish }}
            timeoutInMinutes: ${{ parameters.timeoutInMinutes }}
            restorePreSolution: ${{ parameters.restorePreSolution }}
            restoreSolution: ${{ parameters.restoreSolution }}
            useCache: ${{ parameters.useCache }}
      - ${{ if eq(parameters.type, 'javascript') }}:
        - template: ../javascript/npm-commands.yml
          parameters:
            npmCmds: ${{parameters.npmCmds}}
            nodeVersion: ${{parameters.nodeVersion}}
            skipSonarBranch: ${{ parameters.skipSonarBranch }}
            sonarInclusions: ${{ parameters.sonarInclusions }}
            sonarExclusions: ${{ parameters.sonarExclusions }}
            sonarTestInclusions: ${{ parameters.sonarTestInclusions }}
            sonarExecutionReportPaths: ${{ parameters.sonarExecutionReportPaths }}
            skipSast: ${{ parameters.skipSast }}
            sastFilter: ${{ parameters.sastFilter }}
            scaFilter: ${{ parameters.scaFilter }}
      - ${{ if eq(parameters.type, 'ruby') }}:
        - template: ../ruby/ruby-commands.yml
          parameters:
            rubyCmds: ${{parameters.rubyCmds}}
            rubyVersion: ${{parameters.rubyVersion}}
            skipSonarBranch: ${{ parameters.skipSonarBranch }}
      - ${{ if or(eq(variables['build.sourceBranchName'], 'master'), eq(variables['build.sourceBranchName'], 'main'), contains(variables['build.sourceBranch'], 'hotfix'), contains(variables['build.sourceBranch'], 'beta')) }}:
        - template: ../tools/ProductBuildNumber.yml
          parameters:
            variableGroupName: ${{ parameters.variableGroupName }}
            variableCountName: ${{ parameters.variableCountName }}
            useSharedBuildCount: ${{ parameters.useSharedBuildCount }}
        - ${{ if eq(parameters.createZipChart, true) }}:
          - task: ArchiveFiles@2
            displayName: 'Archive ${{ parameters.chartDir }} folder'
            inputs:
                rootFolderOrFile: ${{ parameters.chartDir }}
                archiveFile: '$(Build.ArtifactStagingDirectory)/Iris.Helm.Chart.$(ProductBuildNumber).zip'
                includeRootFolder: false
          - task: OctopusPush@4
            displayName: 'Publish HelmCharts to Octopus'
            inputs:
              OctoConnectedServiceName: 'blip.octopus.app'
              Space: ${{ parameters.octopusSpace }}
              Package: '$(Build.ArtifactStagingDirectory)/Iris.Helm.Chart.$(ProductBuildNumber).zip'
              Replace: 'false'

        - task: OctopusPack@4
          condition: and(succeeded(),eq(${{ parameters.createZipPackage }}, true))
          inputs:
            PackageId: ${{ parameters.octopusArtifactName }}
            PackageFormat: ${{ parameters.octopusPackageFormat }}
            PackageVersion: '$(ProductBuildNumber)'
            SourcePath: '$(Build.SourcesDirectory)/${{ parameters.buildArtifactsDir }}'
            OutputPath: '$(Build.ArtifactStagingDirectory)'

        - task: OctopusPush@4
          displayName: 'Publish Project to Octopus'
          condition: and(succeeded(),eq(${{ parameters.createZipPackage }}, true))
          inputs:
            OctoConnectedServiceName: 'blip.octopus.app'
            Space: ${{ parameters.octopusSpace }}
            Package: '$(Build.ArtifactStagingDirectory)/${{ parameters.octopusArtifactName }}.$(ProductBuildNumber).${{ lower(parameters.octopusPackageFormat) }}'
            Replace: 'false'

        - task: PowerShell@2
          displayName: 'Prepare Config Settings to Octopus'
          condition: and(succeeded(), ne('${{ parameters.configPath }}', ''))
          inputs:
            targetType: 'inline'
            script: |
              echo $(ProductBuildNumber)
              mkdir $(Build.SourcesDirectory)/Y29uZmlnU3JjCg==
              cp $(Build.SourcesDirectory)/${{ parameters.configPath }} $(Build.SourcesDirectory)/Y29uZmlnU3JjCg==/

        - task: OctopusPack@4
          displayName: 'Packing Config Settings to Octopus'
          condition: and(succeeded(), ne('${{ parameters.configPath }}', ''))
          inputs:
            PackageId: ${{ parameters.packagename }}__config
            PackageFormat: 'Zip'
            PackageVersion: '$(ProductBuildNumber)'
            SourcePath: '$(Build.SourcesDirectory)/Y29uZmlnU3JjCg=='
            OutputPath: '$(Build.ArtifactStagingDirectory)'

        - task: OctopusPush@4
          displayName: 'Publish Config Settings to Octopus'
          condition: and(succeeded(), ne('${{ parameters.configPath }}', ''))
          inputs:
            OctoConnectedServiceName: 'blip.octopus.app'
            Space: ${{ parameters.octopusSpace }}
            Package: '$(Build.ArtifactStagingDirectory)/${{ parameters.packagename }}__config.$(ProductBuildNumber).zip'
            Replace: 'false'
            
        - ${{ if containsValue(parameters.octopusReleaseChannel, 'Default') }}:
          - task: OctopusCreateRelease@4
            displayName: 'Create Octopus Release'
            inputs:
              OctoConnectedServiceName: 'blip.octopus.app'
              Space: ${{ parameters.octopusSpace }}
              ProjectName: ${{ parameters.octopuspackagename }}
              ${{ if contains(variables['build.sourceBranch'], 'beta') }}:
                ReleaseNumber: $(ProductBuildNumber)-beta
              ${{  else  }}:
                ReleaseNumber: $(ProductBuildNumber)
              Channel: Default
              ${{ if eq(parameters.hasFrontEndDeploy, true) }}:
                AdditionalArguments: "--package=*:$(ProductBuildNumber) --package=${{ parameters.packagename }}__config:$(ProductBuildNumber) --package helm-v3:15.2-windows-amd64 --package ${{ parameters.frontEndHelmPackageVersion }} --package yq:4.44.3"
              ${{  else  }}:
                AdditionalArguments: "--package=*:$(ProductBuildNumber) --package=${{ parameters.packagename }}__config:$(ProductBuildNumber) --package helm-v3:15.2-windows-amd64 --package yq:4.44.3"
        - ${{  else  }}:
          - ${{ each channel in parameters.octopusReleaseChannel }}:
            - task: OctopusCreateRelease@4
              displayName: 'Create Octopus Release ${{ channel }}'
              inputs:
                OctoConnectedServiceName: 'blip.octopus.app'
                Space: ${{ parameters.octopusSpace }}
                ProjectName: ${{ parameters.octopuspackagename }}
                ${{ if contains(variables['build.sourceBranch'], 'beta') }}:
                  ReleaseNumber: $(ProductBuildNumber)-${{ lower(channel) }}.beta
                ${{  else  }}:
                  ReleaseNumber: $(ProductBuildNumber)-${{ lower(channel) }}
                Channel: ${{ channel }}
                ${{ if eq(parameters.hasFrontEndDeploy, true) }}:
                  AdditionalArguments: "--package=*:$(ProductBuildNumber) --package=${{ parameters.packagename }}__config:$(ProductBuildNumber)  --package helm-v3:15.2-windows-amd64 --package ${{ parameters.frontEndHelmPackageVersion }} --package yq:4.44.3"
                ${{  else  }}:
                  AdditionalArguments: "--package=*:$(ProductBuildNumber) --package=${{ parameters.packagename }}__config:$(ProductBuildNumber)  --package helm-v3:15.2-windows-amd64 --package yq:4.44.3"
        - ${{ if contains(variables['build.sourceBranch'], 'hotfix') }}:
          - template: ../tools/CreateTag.yml
            parameters:
              nameTag: $(ProductBuildNumber)
              isHotfix: true
              isBeta: false
              usePrefix: true
        - ${{ if contains(variables['build.sourceBranch'], 'beta') }}:
          - template: ../tools/CreateTag.yml
            parameters:
              nameTag: $(ProductBuildNumber)
              isHotfix: false
              isBeta: true
              usePrefix: true
        - ${{ if or(eq(variables['build.sourceBranchName'], 'master'), eq(variables['build.sourceBranchName'], 'main')) }}:
          - template: ../tools/CreateTag.yml
            parameters:
              nameTag: $(ProductBuildNumber)
              isHotfix: false
              isBeta: false
              usePrefix: true
  - ${{ if or(eq(variables['build.sourceBranchName'], 'master'), eq(variables['build.sourceBranchName'], 'main'), contains(variables['build.sourceBranch'], 'hotfix'), contains(variables['build.sourceBranch'], 'beta')) }}:
    - job: Docker_Build_Push
      dependsOn: Octopus_Build_Push
      variables:
      - group: ${{ parameters.variableGroupName }}
      - name: skipDecorator
        value: true
      ${{ if ne(parameters.dockerAgentPool, '') }}:
        pool:
            vmImage: ${{ parameters.dockerAgentPool }}
      steps:
      - script: |
          printf "[INFO] Checking out submodules with received parameter \ngitSubmodules: \n${{ convertToJson(parameters.gitSubmodules) }}\n\n"
          gitSubmodulesCustomVersions=$(echo ${gitSubmodulesCustomVersions#\"}) # Remove leading double quote
          gitSubmodulesCustomVersions=$(echo ${gitSubmodulesCustomVersions%\"}) # Remove trailing double quote
          for submodule in $(echo ${gitSubmodulesCustomVersions} | jq -c ".[]"); do
            printf "[INFO] Cloning submodule repository:\n $submodule\n---\n"
            repo="$(echo $submodule | jq -r '.repo')"
            path="./$(echo $submodule | jq -r '.path')"
            ref="$(echo $submodule | jq -r '.ref')"

            if [ -d "$path" ]; then
              cd "$path"
              git init
              git remote add origin $repo
            else
              mkdir -p "$path"
              cd "$path"
              git -c http.extraheader="AUTHORIZATION: bearer $(System.AccessToken)" clone $repo .
            fi

            git -c http.extraheader="AUTHORIZATION: bearer $(System.AccessToken)" fetch --all
            git checkout $ref
            cd -
          done
        displayName: 'Git submodules init'
        condition: and(succeeded(), eq(variables['Agent.OS'], 'Linux'), eq(${{ parameters.gitSubmodules.enabled }}, true))
        env:
          gitSubmodulesCustomVersions: ${{ convertToJSON(parameters.gitSubmodules.customVersions) }}
      - template: ../tools/ProductBuildNumber.yml
        parameters:
          variableGroupName: ${{ parameters.variableGroupName }}
          variableCountName: ${{ parameters.variableCountName }}
          useSharedBuildCount: ${{ parameters.useSharedBuildCount }}
      - ${{ if eq(parameters.dockerBuildPush, true) }}:
        - template: ../docker/template-ci.yml
          parameters:
            svcConnectionDocker: "Blip ACR - blipcontainerregistry"
            dockerfiledir: ${{ parameters.dockerfiledir }}
            contextpath: ${{ parameters.contextpath }}
            ${{ if eq(parameters.dockerRepositoryName, '') }}:
              repositoryName: "${{ parameters.packagename }}"
            ${{ else }}:
              repositoryName: ${{ parameters.dockerRepositoryName }}
            tags: ${{ parameters.dockerTags }}
            dockerArguments: '${{ parameters.dockerArguments }}'

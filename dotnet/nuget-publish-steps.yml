parameters:
  - name: solution
    type: string
    default: '**/*.sln'
    displayName: 'Solution file pattern to pack'
  - name: buildConfiguration
    type: string
    default: 'release'
    displayName: 'Build configuration'
  - name: vstsFeed
    type: string
    default: ''
    displayName: 'Azure Artifacts feed name'

steps:
  - task: DotNetCoreCLI@2
    displayName: 'Pack NuGet packages from solution'
    inputs:
      command: 'pack'
      packagesToPack: '${{ parameters.solution }}'
      versioningScheme: 'byBuildNumber'
      outputDir: '$(Build.ArtifactStagingDirectory)/nuget'
      includesymbols: true

  - ${{ if ne(parameters.vstsFeed, '') }}:
    - task: DotNetCoreCLI@2
      displayName: 'Push NuGet packages to feed'
      inputs:
        command: 'push'
        packagesToPush: '$(Build.ArtifactStagingDirectory)/nuget/*.nupkg;!$(Build.ArtifactStagingDirectory)/nuget/*.symbols.nupkg'
        nuGetFeedType: 'internal'
        publishVstsFeed: '${{ parameters.vstsFeed }}'

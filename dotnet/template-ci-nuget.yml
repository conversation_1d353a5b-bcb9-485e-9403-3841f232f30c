parameters:
- name: buildConfiguration
  type: string
  default: release
- name: from
  type: string
  default: csps
- name: packagename    
  type: string
  default: ''
- name: coverageTest
  type: boolean
  default: false
- name: includeSymbols
  type: boolean
  default: false
- name: testParameters
  type: string
  default: '/p:CollectCoverage=true /p:CoverletOutputFormat=opencover /p:CoverletOutput=$(Build.SourcesDirectory)/coverage/'
- name: testProjects
  type: string
  default: '**/*Test*.csproj'
- name: dotnetVersion
  type: string
  default: ''
- name: testPermission
  type: boolean
  default: false
- name: skipTestToNuget
  type: boolean
  default: false
- name: vstsFeed
  type: string
  default: ''
- name: skipSonarBranch
  type: boolean
- name: skipSast
  type: boolean
  default: true
- name: sastFilter
  type: string
  default: '**/*'
- name: scaFilter
  type: string
  default: '**/*'
- name: solution
  type: string
  default: '**/*.sln' 
- name: csprojToPack
  type: object
  default: 
    - 'none'
- name: nugetAuthenticate
  type: boolean
  default: false

jobs:
- job: "build"  
  displayName: "Build a Project Package"  
  steps:
  - checkout: self
    persistCredentials: true
  - task: Assembly-Info-NetCore@2
    inputs:
      Path: '$(Build.SourcesDirectory)'
      FileNames: '**/*.csproj'
      InsertAttributes: false
      FileEncoding: 'auto'
      WriteBOM: false
      VersionNumber: '$(Build.BuildNumber)'
      FileVersionNumber: '$(Build.BuildNumber)'
      InformationalVersion: '$(Build.BuildNumber)'
      LogLevel: 'verbose'
      FailOnWarning: false
      DisableTelemetry: false
  - template: ./dotnet-commands.yml
    parameters:            
      buildConfiguration: ${{parameters.buildConfiguration}}       
      coverageTest: ${{parameters.coverageTest}}  
      testParameters: ${{ parameters.testParameters }}        
      testPermission: ${{ parameters.testPermission }}
      skipTestToNuget: ${{ parameters.skipTestToNuget }}
      testProjects: ${{ parameters.testProjects }}
      dotnetVersion: ${{ parameters.dotnetVersion }}
      vstsFeed: ${{ parameters.vstsFeed }}
      skipSonarBranch: ${{ parameters.skipSonarBranch }}
      skipSast: ${{ parameters.skipSast }}
      sastFilter: ${{ parameters.sastFilter }}
      scaFilter: ${{ parameters.scaFilter }}
      solution: ${{ parameters.solution }}
      nugetAuthenticate: ${{ parameters.nugetAuthenticate }}
  - ${{ if containsValue(parameters.csprojToPack, 'none') }}:
    - task: DotNetCoreCLI@2
      displayName: 'Packing your project'
      inputs:
        command: 'custom'
        custom: 'pack'
        projects: '**/*.sln'
        packagesToPack: '**/*.sln'
        versioningScheme: 'byBuildNumber'
        ${{ if startsWith(variables['Build.SourceBranch'], 'refs/heads/beta/') }}:
          arguments: --configuration ${{parameters.buildConfiguration}} --include-symbols --output $(Build.ArtifactStagingDirectory) /p:Version=$(Build.BuildNumber)-beta
        ${{ else }}:
          arguments: --configuration ${{parameters.buildConfiguration}} --include-symbols --output $(Build.ArtifactStagingDirectory) /p:Version=$(Build.BuildNumber)
        includesymbols: ${{parameters.includeSymbols}} 
  - ${{ else }}:      
    - ${{ each csproj in parameters.csprojToPack }}:
      - task: DotNetCoreCLI@2
        displayName: 'Packing your project ${{ csproj }}'
        inputs:
          command: 'custom'
          custom: 'pack'
          projects: '**/${{ csproj }}'
          packagesToPack: '**/${{ csproj }}'
          versioningScheme: 'byBuildNumber'
          ${{ if startsWith(variables['Build.SourceBranch'], 'refs/heads/beta/') }}:
            arguments: --configuration ${{parameters.buildConfiguration}} --include-symbols --output $(Build.ArtifactStagingDirectory) /p:Version=$(Build.BuildNumber)-beta
          ${{ else }}:
            arguments: --configuration ${{parameters.buildConfiguration}} --include-symbols --output $(Build.ArtifactStagingDirectory) /p:Version=$(Build.BuildNumber)
          includesymbols: ${{parameters.includeSymbols}}
  - ${{ if contains(variables['build.sourceBranch'], 'hotfix') }}: 
    - template: ../tools/CreateTag.yml
      parameters:
        nameTag: $(Build.BuildNumber)
        isHotfix: true
        isBeta: false
        usePrefix: true
  - ${{ if contains(variables['build.sourceBranch'], 'beta') }}: 
    - template: ../tools/CreateTag.yml
      parameters:
        nameTag: $(Build.BuildNumber)
        isHotfix: false
        isBeta: true
        usePrefix: true
  - ${{ if or(eq(variables['build.sourceBranchName'], 'master'), eq(variables['build.sourceBranchName'], 'main')) }}:
    - template: ../tools/CreateTag.yml
      parameters:
        nameTag: $(Build.BuildNumber)
        isHotfix: false
        isBeta: false
        usePrefix: true
  - task: PublishBuildArtifacts@1
    displayName: Publish Nupkg to Artifact
    inputs:
      PathtoPublish: $(Build.ArtifactStagingDirectory)
      ArtifactName: NugetPublish
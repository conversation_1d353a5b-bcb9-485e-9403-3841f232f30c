# NuGet Publish Steps Template

## Overview

The `nuget-publish-steps.yml` template provides reusable steps for packaging and publishing .NET NuGet packages to Azure Artifacts feeds.

## Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `solution` | string | `**/*.sln` | Solution file pattern to pack |
| `buildConfiguration` | string | `release` | Build configuration for packaging |
| `vstsFeed` | string | `''` | Azure Artifacts feed name for publishing |

## What It Does

1. **Authenticates** with Azure Artifacts using `NuGetAuthenticate@1`
2. **Packs** all packable projects in the solution using `dotnet pack`
3. **Includes** symbol packages for debugging support
4. **Publishes** packages to the specified Azure Artifacts feed (if provided)
5. **Versions** packages using `$(ProductBuildNumber)` with beta suffix support

## Usage

### Standalone Usage

```yaml
steps:
  - template: dotnet/nuget-publish-steps.yml
    parameters:
      solution: 'src/MyProject.sln'
      buildConfiguration: 'Release'
      vstsFeed: 'MyFeed'
```

### Integration in dotnet-commands.yml

```yaml
# In dotnet-commands.yml
- ${{ if eq(parameters.publishNugetPackages, true) }}:
  - template: ./nuget-publish-steps.yml
    parameters:
      solution: ${{ parameters.solution }}
      buildConfiguration: ${{ parameters.buildConfiguration }}
      vstsFeed: ${{ parameters.vstsFeed }}
```

## Prerequisites

- `$(ProductBuildNumber)` variable must be available (typically set by ProductBuildNumber.yml template)
- Azure Artifacts feed must be configured and accessible
- Projects must have appropriate `<IsPackable>` settings
- Called from within the dotnet build process (e.g., from dotnet-commands.yml)

## Output

- NuGet packages (`.nupkg`) are created in `$(Build.ArtifactStagingDirectory)/nuget/`
- Symbol packages (`.symbols.nupkg`) are created but not published
- Only regular packages are pushed to the feed

## Versioning

- **Regular branches**: Uses `$(ProductBuildNumber)` as package version
- **Beta branches**: Uses `$(ProductBuildNumber)-beta` as package version
- Version is applied via MSBuild `PackageVersion` property

## Notes

- The template automatically excludes symbol packages from publishing
- Authentication is always performed for secure feed access
- If no `vstsFeed` is provided, packages are only created (not published)
- All packable projects in the solution are automatically discovered and packed

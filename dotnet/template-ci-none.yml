parameters:
- name: buildConfiguration
  type: string
  default: release
- name: from
  type: string
  default: csps
- name: packagename    
  type: string
  default: ''
- name: coverageTest
  type: boolean
  default: false
- name: testParameters
  type: string
  default: '/p:CollectCoverage=true /p:CoverletOutputFormat=opencover /p:CoverletOutput=$(Build.SourcesDirectory)/coverage/'
- name: testProjects
  type: string
  default: '**/*Test*.csproj'
- name: dotnetVersion
  type: string
  default: ''
- name: vstsFeed
  type: string
  default: ''
- name: skipSonarBranch
  type: boolean

jobs:
- job: "build"  
  displayName: "Build a Project Package"  
  steps:
  - template: ./dotnet-commands.yml
    parameters:            
      buildConfiguration: ${{parameters.buildConfiguration}}       
      coverageTest: ${{parameters.coverageTest}}  
      testParameters: ${{ parameters.testParameters }}        
      testProjects: ${{ parameters.testProjects }}
      dotnetVersion: ${{ parameters.dotnetVersion }}
      vstsFeed: ${{ parameters.vstsFeed }}
      skipSonarBranch: ${{ parameters.skipSonarBranch }}
      nugetAuthenticate: ${{ parameters.nugetAuthenticate }}
  - ${{ if ne(parameters.dotnetVersion, '') }}:
    - task: UseDotNet@2
      displayName: 'Use .NET Core sdk'
      inputs:
        packageType: sdk
        version: "5.0.0"
        installationPath: $(Agent.ToolsDirectory)/dotnet
  - task: DotNetCoreCLI@2
    displayName: 'dotnet publish'
    inputs:
      command: publish
      arguments: '-o ./publish'
  - task: PublishBuildArtifacts@1
    displayName: Publish Package
    inputs:
      PathtoPublish: ./publish/
      ArtifactName: ApiAppsettings
parameters:
- name: buildConfiguration
  type: string
  default: release   
- name: coverageTest
  type: boolean
  default: false
- name: testProjects
  type: string
  default: '**/*Test*.csproj'
- name: testParameters
  type: string
  default: '/p:CollectCoverage=true /p:CoverletOutputFormat=opencover /p:CoverletOutput=$(Build.SourcesDirectory)/coverage/'
- name: publishTestResults
  type: string
  default: 'none'
- name: dotnetVersion
  type: string
  default: ''
- name: dotnetPublish
  type: boolean
  default: false
- name: testPermission
  type: boolean
  default: false
- name: skipTestToNuget
  type: boolean
  default: false
- name: vstsFeed
  type: string
  default: ''
- name: skipSonarBranch
  type: boolean
- name: sonarInclusions
  type: string
  default: '**/*'
- name: sonarExclusions
  type: string
  default: ''
- name: sonarTestInclusions
  type: string
  default: '**/*'  
- name: skipSast
  type: boolean
  default: true
- name: sastFilter
  type: string
  default: '**/*'
- name: scaFilter
  type: string
  default: '**/*'
- name: preSolution
  type: string
  default: ''
- name: solution
  type: string
  default: '**/*.sln'   
- name: multiTests
  type: object
  default: 
      - 'none'
- name: csprojToPublish
  type: object
  default: 
    - 'none'
- name: timeoutInMinutes
  type: string
  default: '60'
- name: restorePreSolution
  type: boolean
  default: true
- name: restoreSolution
  type: boolean
  default: true
- name: useCache
  type: boolean
  default: true
- name: nugetAuthenticate
  type: boolean
  default: false
- name: publishNugetPackages
  type: boolean
  default: false

steps:
  - ${{ if eq(parameters.nugetAuthenticate, true) }}: 
    - task: NuGetAuthenticate@1
  - ${{ if ne(parameters.dotnetVersion, '') }}:
    - task: UseDotNet@2
      displayName: 'Use .NET Core sdk'
      inputs:
        packageType: sdk
        version: ${{ parameters.dotnetVersion }}
        installationPath: $(Agent.ToolsDirectory)/dotnet
        includePreviewVersions: true
    - ${{ if eq(parameters.dotnetVersion, '2.2.x') }}:
      - task: UseDotNet@2
        displayName: 'Use .NET Core runtime 3.1.x for sdk 2.2.x'
        inputs:
          packageType: runtime
          version: 3.1.x
          installationPath: $(Agent.ToolsDirectory)/dotnet
  - ${{ if eq(parameters.skipSast, false) }}:
    - template: ../si/sast-analysis.yml
      parameters:
        sastFilter: ${{ parameters.sastFilter }}
        scaFilter: ${{ parameters.scaFilter }}
  - ${{ if ne(parameters.preSolution, '') }}:
    - ${{ if eq(parameters.restorePreSolution, true) }}:
      - task: DotNetCoreCLI@2
        displayName: Restore PreSolution
        inputs:
          command: restore
          projects: ${{ parameters.preSolution }}
          ${{ if ne(parameters.vstsFeed, '') }}:
            vstsFeed: ${{ parameters.vstsFeed }}
            includeNuGetOrg: true
    - task: DotNetCoreCLI@2
      displayName: Build PreSolution
      inputs:
        projects: ${{ parameters.preSolution }} 
        arguments: --configuration ${{parameters.buildConfiguration}}
    - ${{ if eq(parameters.skipSonarBranch, false) }}:
      - template: ../si/pre-steps-template.yml
        parameters:
          sonarInclusions: ${{ parameters.sonarInclusions }}
          sonarExclusions: ${{ parameters.sonarExclusions }}
          sonarTestInclusions: ${{ parameters.sonarTestInclusions }}
          type: dotnet
    - ${{ if eq(parameters.restoreSolution, true) }}:
      - task: DotNetCoreCLI@2
        displayName: Restore Solution
        inputs:
          command: restore
          projects: ${{ parameters.solution }}
          ${{ if ne(parameters.vstsFeed, '') }}:
            vstsFeed: ${{ parameters.vstsFeed }}
            includeNuGetOrg: true
    - task: DotNetCoreCLI@2
      displayName: Build Solution
      inputs:
        projects: ${{ parameters.solution }} 
        arguments: --configuration ${{parameters.buildConfiguration}}
  - ${{ else }}:
    - ${{ if eq(parameters.skipSonarBranch, false) }}:
      - template: ../si/pre-steps-template.yml
        parameters:
          sonarInclusions: ${{ parameters.sonarInclusions }}
          sonarExclusions: ${{ parameters.sonarExclusions }}
          sonarTestInclusions: ${{ parameters.sonarTestInclusions }}
          type: dotnet
    - ${{ if eq(parameters.useCache, true) }}:
      - task: DotNetCoreCLI@2
        displayName: Install dotnet-subset tool
        inputs:
          command: custom
          custom: tool
          arguments: install --global dotnet-subset-slnf --version 0.3.2
      - task: DotNetCoreCLI@2
        displayName: Generate subset restore files
        inputs:
          command: custom
          custom: subset
          arguments: restore ${{ parameters.solution }} --root-directory $(Build.SourcesDirectory) --output $(Agent.TempDirectory)/restore_subset/
      - task: Cache@2
        displayName: Cache restore files
        inputs:
          key: 'nuget | "$(Agent.OS)" | $(Agent.TempDirectory)/restore_subset/**/*'
          restoreKeys: |
            nuget | "$(Agent.OS)"
            nuget
          path: $(NUGET_PACKAGES)
          cacheHitVar: 'CACHE_RESTORED'
        continueOnError: true        
    - ${{ if eq(parameters.restoreSolution, true) }}:
      - ${{ if eq(parameters.useCache, true)  }}:
        - task: DotNetCoreCLI@2
          displayName: Restore with Cache
          inputs:
            command: restore
            projects: ${{ parameters.solution }}
            arguments: --locked-mode
            ${{ if ne(parameters.vstsFeed, '') }}:
              vstsFeed: ${{ parameters.vstsFeed }}
              includeNuGetOrg: true    
      - ${{ else }}:
        - task: DotNetCoreCLI@2
          displayName: Restore
          inputs:
            command: restore
            projects: ${{ parameters.solution }}
            ${{ if ne(parameters.vstsFeed, '') }}:
              vstsFeed: ${{ parameters.vstsFeed }}
              includeNuGetOrg: true
      - task: DotNetCoreCLI@2
        displayName: Build already restored
        inputs:
          projects: ${{ parameters.solution }} 
          arguments: --configuration ${{parameters.buildConfiguration}} --no-restore
    - ${{ else }}:
      - task: DotNetCoreCLI@2
        displayName: Build
        inputs:
          projects: ${{ parameters.solution }} 
          arguments: --configuration ${{parameters.buildConfiguration}}
  - ${{ if eq(parameters.skipTestToNuget, false) }}:
    - ${{ if eq(parameters.testPermission, false) }}:
      - ${{ if containsValue(parameters.multiTests, 'none') }}:
        - task: DotNetCoreCLI@2
          displayName: Test
          inputs:
            command: custom
            projects: ${{ parameters.testProjects }}
            custom: test
            arguments: ${{ parameters.testParameters }}
            publishTestResults: true
      - ${{ else }}:
        - ${{ each test in parameters.multiTests }}: 
          - task: DotNetCoreCLI@2
            displayName: ${{ test.testname }} Test
            timeoutInMinutes: ${{ parameters.timeoutInMinutes }} 
            inputs:
              command: custom
              projects: ${{ test.projects }}
              custom: test
              arguments: ${{ test.filters }}
              publishTestResults: true
      - task: PublishTestResults@2
        inputs:
          testResultsFormat: 'VSTest'
          testResultsFiles: ${{ parameters.publishTestResults }}
          mergeTestResults: true
        condition: not(contains('${{ parameters.publishTestResults }}', 'none'))
        continueOnError: true
    - ${{ if eq(parameters.testPermission, true) }}:
      - ${{ if containsValue(parameters.multiTests, 'none') }}:
        - task: Bash@3
          displayName: Test with Permission
          inputs:
            targetType: 'inline'
            script: |
              sudo dotnet test ${{ parameters.testProjects }} --no-restore --verbosity normal
          
              pwd 
          
              ls     
      - ${{ else }}:          
        - ${{ each test in parameters.multiTests }}:
          - task: Bash@3
            displayName: Test with Permission
            inputs:
              targetType: 'inline'
              script: |
                sudo dotnet test ${{ test.projects }} --no-restore --verbosity normal
            
                pwd 
            
                ls
    - ${{ if eq(parameters.coverageTest, true) }}:
      - task: reportgenerator@4
        displayName: 'Generate coverage report'
        inputs:
          reports: '$(Build.SourcesDirectory)/**/coverage.opencover.xml'
          targetdir: '$(Build.SourcesDirectory)/coverlet/reports'
      - task: PublishCodeCoverageResults@1
        displayName: 'Publish coverage report'
        inputs:
          codeCoverageTool: 'Cobertura'
          summaryFileLocation: '$(Build.SourcesDirectory)/coverlet/reports/Cobertura.xml'
  - ${{ if eq(parameters.dotnetPublish, true) }}:
    - ${{ if or(in(variables['build.sourceBranchName'], 'master', 'main', 'develop'), contains(variables['build.sourceBranch'], 'beta'), contains(variables['build.sourceBranch'], 'hotfix')) }}:
      - ${{ if containsValue(parameters.csprojToPublish, 'none') }}:
        - task: DotNetCoreCLI@2
          displayName: 'Publish'
          inputs:
            command: publish
            publishWebProjects: True
            arguments: '--configuration ${{parameters.buildConfiguration}} --output ./Releases'
            zipAfterPublish: False
      - ${{ else }}:
        - ${{ each csproj in parameters.csprojToPublish }}:
          - task: DotNetCoreCLI@2
            displayName: 'Publish ${{ csproj }}'
            inputs:
              command: publish
              projects: '**/${{ csproj }}'
              arguments: '--configuration ${{parameters.buildConfiguration}} --output ./Releases'
              zipAfterPublish: False
              publishWebProjects: False
  # NuGet Package and Publish Steps
  - ${{ if eq(parameters.publishNugetPackages, true) }}:
    - template: nuget-publish-steps.yml
      parameters:
        solution: ${{ parameters.solution }}
        buildConfiguration: ${{ parameters.buildConfiguration }}
        vstsFeed: ${{ parameters.vstsFeed }}
  - ${{ if eq(parameters.skipSonarBranch, false) }}:
    - template: ../si/pos-steps-template.yml      
  - ${{ if ne(parameters.vstsFeed, '') }}:
    - task: qetza.replacetokens.replacetokens-task.replacetokens@3
      displayName: 'Replace tokens in **/Dockerfile'
      inputs:
        targetFiles: '**/Dockerfile'
        tokenPrefix: '$('
        tokenSuffix: ')'
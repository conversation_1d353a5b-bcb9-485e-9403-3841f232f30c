parameters:
- name: packagename
  type: string    
  displayName: 'Name of the Project'

- name: nugetServiceConnection
  type: string    
  displayName: 'Name of the service connection of nuget service'
  default: ''

jobs:
- deployment: cd_nuget_publish
  environment: NugetDeploy        
  variables:
    skipDecorator: true
    skipSonarBranch: true
  strategy:
    runOnce:
      deploy:
        steps:
        - task: DeleteFiles@1
          displayName: 'Delete files from $(Agent.BuildDirectory)/NugetPublish/'
          inputs:
            SourceFolder: '$(Agent.BuildDirectory)/NugetPublish/'
            Contents: '**.symbols.nupkg'    
        - task: NuGetCommand@2
          displayName: Publish Nuget Package To Nuget.org
          inputs:
            command: 'push'            
            packagesToPush: '$(Agent.BuildDirectory)/NugetPublish/*.nupkg'
            nuGetFeedType: 'external'
            publishFeedCredentials: '${{parameters.nugetServiceConnection}}'
            allowPackageConflicts: true
            arguments: '-SkipDuplicate'


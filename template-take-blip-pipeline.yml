trigger:
  - master

pool:
  vmImage: ubuntu-latest

variables:
  SkipDecorator: true

steps:
  - checkout: self
    persistCredentials: true
    fetchDepth: 0

  - script: git checkout master
    displayName: Checkout to branch master

  - script: |
      git config --global user.email "<EMAIL>"
      git config --global user.name "User Devops"
    displayName: Configure git credentials

  - task: UseNode@1
    displayName: Install NodeJS 22 LTS
    inputs:
      version: "22.x"

  - script: npm install -g release-it@17.10.0
    displayName: Install release-it

  - script: npx release-it --ci --git.requireBranch=master
    displayName: Generate new tag version

  - script: |
      git tag -d latest
      git push -d origin latest
      git tag latest
      git push origin latest
    displayName: Move tag latest
